<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ContactQuery;
use App\Exports\ContactQueriesExport;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class ContactQueryController extends Controller
{
    public function index(Request $request)
    {
        $query = ContactQuery::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('company_name', 'like', "%{$search}%")
                    ->orWhere('message', 'like', "%{$search}%");
            });
        }

        // Date filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        $contactQueries = $query->latest()->paginate(15);

        return inertia('Admin/ContactQueries/Index', [
            'contactQueries' => $contactQueries,
            'filters' => $request->only(['search', 'date_from', 'date_to']),
        ]);
    }

    public function export()
    {
        return Excel::download(new ContactQueriesExport, 'contact-queries.xlsx');
    }

    public function destroy(ContactQuery $contactQuery)
    {
        $contactQuery->delete();

        return redirect()->route('admin.contact-queries.index')->with('success', 'Contact query deleted successfully.');
    }

    public function bulkDestroy(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:contact_queries,id',
        ]);

        ContactQuery::whereIn('id', $request->ids)->delete();

        return redirect()->route('admin.contact-queries.index')->with('success', 'Selected contact queries deleted successfully.');
    }

    /**
     * Get new contact queries for real-time updates
     */
    public function getNewQueries(Request $request)
    {
        $request->validate([
            'since' => 'required|date',
        ]);

        $since = Carbon::parse($request->get('since'));

        $newQueries = ContactQuery::where('created_at', '>', $since)
            ->latest()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $newQueries,
            'count' => $newQueries->count(),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Check for real-time updates based on cache timestamps
     */
    public function checkUpdates(Request $request)
    {
        $lastCheck = $request->get('last_check', 0);
        $lastUpdate = Cache::get('contact_queries_updated', 0);

        if ($lastUpdate > $lastCheck) {
            // Get the latest queries
            $queries = ContactQuery::latest()->take(10)->get();

            return response()->json([
                'success' => true,
                'has_updates' => true,
                'data' => $queries,
                'last_update' => $lastUpdate,
                'timestamp' => now()->toISOString(),
            ]);
        }

        return response()->json([
            'success' => true,
            'has_updates' => false,
            'last_update' => $lastUpdate,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get only the cache timestamp for lightweight checking
     */
    public function getCacheTimestamp()
    {
        $lastUpdate = Cache::get('contact_queries_updated', 0);

        return response()->json([
            'success' => true,
            'cache_timestamp' => $lastUpdate,
            'timestamp' => now()->toISOString(),
        ]);
    }
}
