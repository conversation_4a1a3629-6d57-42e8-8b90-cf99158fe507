<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Article extends Model
{
    use HasFactory;

    protected $fillable = [
        'slug',
        'image',
        'date',
        'blog_tag_id',
        'author_id',
        'title',
        'excerpt',
        'content',
        'read_time',
        'is_featured',
    ];

    protected $casts = [
        'content' => 'array',
        'is_featured' => 'boolean',
    ];

    /**
     * Get the blog tag associated with this article.
     */
    public function blogTag()
    {
        return $this->belongsTo(BlogTag::class);
    }

    /**
     * Get the author of this article.
     */
    public function author()
    {
        return $this->belongsTo(Author::class);
    }
}