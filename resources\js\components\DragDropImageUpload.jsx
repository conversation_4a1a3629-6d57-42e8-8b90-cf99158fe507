import { useState, useRef } from "react";
import {
    ImageSquareIcon,
    PlusIcon,
    XIcon,
    ArrowsOutCardinalIcon,
} from "@phosphor-icons/react";

const DragDropImageUpload = ({
    onFileSelect,
    onInsert = null,
    preview = null,
    onRemove = null,
    onReorder = null,
    accept = "image/*",
    multiple = false,
    className = "",
    label = "Upload image",
    description = "PNG, JPG, GIF up to 2MB",
}) => {
    const [isDragOver, setIsDragOver] = useState(false);
    const [draggedIndex, setDraggedIndex] = useState(null);
    const fileInputRef = useRef(null);

    const handleDragOver = (e) => {
        e.preventDefault();
        // Only show drag over state if dragging files from outside (not reordering)
        if (e.dataTransfer.types.includes("Files")) {
            setIsDragOver(true);
        }
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        setIsDragOver(false);
    };

    const handleDrop = (e) => {
        e.preventDefault();
        setIsDragOver(false);

        // Only handle file drops, not image reordering drops
        if (e.dataTransfer.types.includes("Files")) {
            const files = Array.from(e.dataTransfer.files);
            const imageFiles = files.filter((file) =>
                file.type.startsWith("image/"),
            );

            if (imageFiles.length > 0) {
                if (multiple) {
                    onFileSelect(imageFiles);
                } else {
                    onFileSelect(imageFiles[0]);
                }
            }
        }
    };

    const handleFileInputChange = (e) => {
        const files = Array.from(e.target.files);
        if (files.length > 0) {
            if (multiple) {
                onFileSelect(files);
            } else {
                onFileSelect(files[0]);
            }
        }
    };

    const handleClick = () => {
        fileInputRef.current?.click();
    };

    const handleInsertClick = (insertIndex) => {
        if (onInsert) {
            // Create a new file input for insertion
            const input = document.createElement("input");
            input.type = "file";
            input.accept = accept;
            input.multiple = multiple;
            input.onchange = (e) => {
                const files = Array.from(e.target.files);
                if (files.length > 0) {
                    onInsert(files, insertIndex);
                }
            };
            input.click();
        } else {
            // Fallback to regular file select
            handleClick();
        }
    };

    // Image reordering handlers
    const handleImageDragStart = (e, index) => {
        setDraggedIndex(index);
        e.dataTransfer.effectAllowed = "move";
        // Set a custom data type to distinguish from file drops
        e.dataTransfer.setData("text/image-reorder", index.toString());
    };

    const handleImageDragOver = (e, index) => {
        e.preventDefault();
        e.stopPropagation(); // Prevent bubbling to main drop zone
        // Only allow drop if we're dragging an image for reordering
        if (e.dataTransfer.types.includes("text/image-reorder")) {
            e.dataTransfer.dropEffect = "move";
        }
    };

    const handleImageDrop = (e, dropIndex) => {
        e.preventDefault();
        e.stopPropagation(); // Prevent the main drop handler from firing

        // Only handle image reordering drops
        if (e.dataTransfer.types.includes("text/image-reorder")) {
            if (
                draggedIndex !== null &&
                draggedIndex !== dropIndex &&
                onReorder
            ) {
                onReorder(draggedIndex, dropIndex);
            }
        }
        setDraggedIndex(null);
    };

    const handleImageDragEnd = () => {
        setDraggedIndex(null);
    };

    return (
        <div className={`relative ${className}`}>
            <div
                className={`flex cursor-pointer justify-center rounded-md border-2 border-dashed px-6 pt-5 pb-6 transition-colors ${
                    isDragOver
                        ? "border-indigo-400 bg-indigo-50"
                        : "border-gray-300 hover:border-gray-400"
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={handleClick}
            >
                <div className="space-y-1 text-center">
                    {preview ? (
                        <div className="relative">
                            {Array.isArray(preview) ? (
                                <div className="grid grid-cols-2 gap-2 sm:grid-cols-3 lg:grid-cols-4">
                                    {preview.map((img, index) => (
                                        <div
                                            key={index}
                                            className={`group relative cursor-move ${
                                                draggedIndex === index
                                                    ? "opacity-50"
                                                    : ""
                                            }`}
                                            draggable={onReorder ? true : false}
                                            onDragStart={(e) =>
                                                handleImageDragStart(e, index)
                                            }
                                            onDragOver={(e) =>
                                                handleImageDragOver(e, index)
                                            }
                                            onDrop={(e) =>
                                                handleImageDrop(e, index)
                                            }
                                            onDragEnd={handleImageDragEnd}
                                        >
                                            <img
                                                src={img}
                                                alt={`Preview ${index + 1}`}
                                                className="h-24 w-full rounded-lg object-cover"
                                            />
                                            {onReorder && (
                                                <div className="absolute top-1 left-1">
                                                    <ArrowsOutCardinalIcon className="h-4 w-4 text-white opacity-0 drop-shadow-lg transition-opacity group-hover:opacity-100" />
                                                </div>
                                            )}
                                            <div className="absolute top-0 right-0 -mt-2 -mr-2 flex space-x-1">
                                                <button
                                                    type="button"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleInsertClick(
                                                            index + 1,
                                                        );
                                                    }}
                                                    className="rounded-full bg-indigo-500 p-1 text-xs text-white opacity-0 transition-opacity group-hover:opacity-100 hover:bg-indigo-600"
                                                    title="Add more images"
                                                >
                                                    <PlusIcon className="h-3 w-3" />
                                                </button>
                                                {onRemove && (
                                                    <button
                                                        type="button"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            onRemove(index);
                                                        }}
                                                        className="rounded-full bg-red-500 p-1 text-xs text-white opacity-0 transition-opacity group-hover:opacity-100 hover:bg-red-600"
                                                        title="Remove image"
                                                    >
                                                        <XIcon className="h-3 w-3" />
                                                    </button>
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="group relative">
                                    <img
                                        src={preview}
                                        alt="Preview"
                                        className="mx-auto h-32 w-auto rounded-lg object-contain"
                                    />
                                    <div className="absolute top-0 right-0 -mt-2 -mr-2 flex space-x-1">
                                        <button
                                            type="button"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleClick();
                                            }}
                                            className="rounded-full bg-indigo-500 p-1 text-xs text-white opacity-0 transition-opacity group-hover:opacity-100 hover:bg-indigo-600"
                                            title="Replace image"
                                        >
                                            <ImageSquareIcon className="h-3 w-3" />
                                        </button>
                                        {onRemove && (
                                            <button
                                                type="button"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    onRemove();
                                                }}
                                                className="rounded-full bg-red-500 p-1 text-xs text-white opacity-0 transition-opacity group-hover:opacity-100 hover:bg-red-600"
                                                title="Remove image"
                                            >
                                                <XIcon className="h-3 w-3" />
                                            </button>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>
                    ) : (
                        <>
                            <ImageSquareIcon className="mx-auto h-12 w-12 text-gray-400" />
                            <div className="flex text-sm text-gray-600">
                                <span className="relative cursor-pointer rounded-md bg-white font-medium text-indigo-600 focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 focus-within:outline-none hover:text-indigo-500">
                                    {label}
                                </span>
                                <p className="pl-1">or drag and drop</p>
                            </div>
                            <p className="text-xs text-gray-500">
                                {description}
                            </p>
                        </>
                    )}
                </div>
            </div>

            <input
                ref={fileInputRef}
                type="file"
                accept={accept}
                multiple={multiple}
                onChange={handleFileInputChange}
                className="sr-only"
            />
        </div>
    );
};

export default DragDropImageUpload;
