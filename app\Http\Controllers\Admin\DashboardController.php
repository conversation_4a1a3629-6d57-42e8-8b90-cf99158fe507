<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Article;
use App\Models\PortfolioCase;
use App\Models\ContactQuery;

class DashboardController extends Controller
{
    public function index()
    {
        $stats = [
            'articles' => Article::count(),
            'cases' => PortfolioCase::count(),
            'contact_queries' => ContactQuery::count(),
            'recent_queries' => ContactQuery::latest()->take(3)->get(),
        ];

        return inertia('Admin/Dashboard', compact('stats'));
    }
}
