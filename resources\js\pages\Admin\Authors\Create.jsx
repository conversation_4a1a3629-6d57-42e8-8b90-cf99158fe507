import { useState } from "react";
import { Head, useForm, Link } from "@inertiajs/react";
import { ArrowLeftIcon } from "@phosphor-icons/react";
import DragDropImageUpload from "../../../components/DragDropImageUpload";

const Create = () => {
    const [imagePreview, setImagePreview] = useState(null);
    const { data, setData, post, processing, errors } = useForm({
        name: "",
        image: null,
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        post("/admin/authors", { forceFormData: true });
    };

    return (
        <>
            <Head title="Create Author" />

            <div className="px-4 sm:px-6 lg:px-8">
                <div className="mb-8">
                    <Link
                        href="/admin/authors"
                        className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
                    >
                        <ArrowLeftIcon className="mr-2 h-4 w-4" />
                        Back to Authors
                    </Link>
                </div>

                <div className="md:grid md:grid-cols-3 md:gap-6">
                    <div className="md:col-span-1">
                        <div className="px-4 sm:px-0">
                            <h3 className="text-lg font-medium leading-6 text-gray-900">
                                Author Information
                            </h3>
                            <p className="mt-1 text-sm text-gray-600">
                                Create a new author for your articles.
                            </p>
                        </div>
                    </div>
                    <div className="mt-5 md:col-span-2 md:mt-0">
                        <form onSubmit={handleSubmit}>
                            <div className="shadow sm:overflow-hidden sm:rounded-md">
                                <div className="space-y-6 bg-white px-4 py-5 sm:p-6">
                                    <div className="grid grid-cols-6 gap-6">
                                        <div className="col-span-6 sm:col-span-4">
                                            <label
                                                htmlFor="name"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Author Name
                                            </label>
                                            <input
                                                type="text"
                                                name="name"
                                                id="name"
                                                value={data.name}
                                                onChange={(e) =>
                                                    setData("name", e.target.value)
                                                }
                                                className="mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                placeholder="Enter author name"
                                            />
                                            {errors.name && (
                                                <p className="mt-1 text-sm text-red-600">
                                                    {errors.name}
                                                </p>
                                            )}
                                        </div>

                                        <div className="col-span-6">
                                            <label className="block text-sm font-medium text-gray-700">
                                                Author Image
                                            </label>
                                            <div className="mt-1">
                                                <DragDropImageUpload
                                                    onFileSelect={(file) => {
                                                        setData("image", file);
                                                        const reader = new FileReader();
                                                        reader.onload = (e) =>
                                                            setImagePreview(e.target.result);
                                                        reader.readAsDataURL(file);
                                                    }}
                                                    preview={imagePreview}
                                                    onRemove={() => {
                                                        setImagePreview(null);
                                                        setData("image", null);
                                                    }}
                                                    label="Upload author image"
                                                    description="PNG, JPG, GIF up to 2MB"
                                                />
                                            </div>
                                            {errors.image && (
                                                <p className="mt-1 text-sm text-red-600">
                                                    {errors.image}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </div>
                                <div className="bg-gray-50 px-4 py-3 text-right sm:px-6">
                                    <div className="flex justify-end space-x-3">
                                        <Link
                                            href="/admin/authors"
                                            className="inline-flex justify-center rounded-md border border-gray-300 bg-white py-2 px-4 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                        >
                                            Cancel
                                        </Link>
                                        <button
                                            type="submit"
                                            disabled={processing}
                                            className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50"
                                        >
                                            {processing ? "Creating..." : "Create Author"}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </>
    );
};

export default Create;
