<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Author;
use Illuminate\Support\Facades\Storage;

class AuthorController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $authors = Author::withCount('articles')->latest()->paginate(10);
        return inertia('Admin/Authors/Index', compact('authors'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return inertia('Admin/Authors/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:authors,name',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('articles/authors', 'public');
            $validated['image'] = '/storage/' . $imagePath;
        }

        Author::create($validated);

        return redirect()->route('admin.authors.index')->with('success', 'Author created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Author $author)
    {
        $author->load([
            'articles' => function ($query) {
                $query->with('blogTag')->latest()->take(10);
            }
        ]);

        return inertia('Admin/Authors/Show', compact('author'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Author $author)
    {
        return inertia('Admin/Authors/Edit', compact('author'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Author $author)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:authors,name,' . $author->id,
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if it exists
            if ($author->image) {
                $oldImagePath = str_replace('/storage/', '', $author->image);
                Storage::disk('public')->delete($oldImagePath);
            }

            $imagePath = $request->file('image')->store('articles/authors', 'public');
            $validated['image'] = '/storage/' . $imagePath;
        }

        $author->update($validated);

        return redirect()->route('admin.authors.index')->with('success', 'Author updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Author $author)
    {
        // Check if author has articles
        if ($author->articles()->count() > 0) {
            if (request()->wantsJson()) {
                return response()->json(['error' => 'Cannot delete author with existing articles.'], 422);
            }
            return redirect()->route('admin.authors.index')->with('error', 'Cannot delete author with existing articles.');
        }

        // Delete image if it exists
        if ($author->image) {
            $imagePath = str_replace('/storage/', '', $author->image);
            Storage::disk('public')->delete($imagePath);
        }

        $author->delete();

        if (request()->wantsJson()) {
            return response()->json(['success' => 'Author deleted successfully.']);
        }
        return redirect()->route('admin.authors.index')->with('success', 'Author deleted successfully.');
    }
}
