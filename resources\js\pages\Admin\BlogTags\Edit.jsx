import { Head, useForm, router } from "@inertiajs/react";
import { ArrowLeftIcon } from "@phosphor-icons/react";

const Edit = ({ tag }) => {
    const { data, setData, post, processing, errors } = useForm({
        name: tag.name || "",
        color: tag.color || "#3B82F6",
        description: tag.description || "",
        is_active: tag.is_active ?? true,
        _method: "PUT",
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        post(`/admin/blog-tags/${tag.id}`);
    };

    const colorPresets = [
        "#3B82F6", // Blue
        "#EF4444", // Red
        "#10B981", // Green
        "#F59E0B", // Yellow
        "#8B5CF6", // Purple
        "#EC4899", // Pink
        "#06B6D4", // Cyan
        "#84CC16", // Lime
        "#F97316", // Orange
        "#6B7280", // Gray
    ];

    return (
        <>
            <Head title={`Edit Tag - ${tag.name}`} />

            <div className="min-h-screen bg-gray-50 py-6">
                <div className="mx-auto max-w-3xl px-4 sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center">
                            <button
                                onClick={() => router.visit("/admin/blog-tags")}
                                className="mr-4 p-2 text-gray-400 hover:text-gray-600"
                            >
                                <ArrowLeftIcon className="h-5 w-5" />
                            </button>
                            <div>
                                <h1 className="text-2xl font-semibold text-gray-900">
                                    Edit Blog Tag
                                </h1>
                                <p className="mt-1 text-sm text-gray-600">
                                    Update the blog tag information
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Form */}
                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="bg-white px-4 py-5 shadow sm:rounded-lg sm:p-6">
                            <div className="md:grid md:grid-cols-3 md:gap-6">
                                <div className="md:col-span-1">
                                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                                        Tag Information
                                    </h3>
                                    <p className="mt-1 text-sm text-gray-500">
                                        Basic information about the blog tag
                                    </p>
                                </div>
                                <div className="mt-5 md:col-span-2 md:mt-0">
                                    <div className="grid grid-cols-6 gap-6">
                                        <div className="col-span-6">
                                            <label
                                                htmlFor="name"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Name
                                            </label>
                                            <input
                                                type="text"
                                                name="name"
                                                id="name"
                                                value={data.name}
                                                onChange={(e) =>
                                                    setData("name", e.target.value)
                                                }
                                                className="mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                placeholder="e.g., Technology, Design, Business"
                                            />
                                            {errors.name && (
                                                <p className="mt-1 text-sm text-red-600">
                                                    {errors.name}
                                                </p>
                                            )}
                                        </div>

                                        <div className="col-span-6">
                                            <label
                                                htmlFor="description"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Description (Optional)
                                            </label>
                                            <textarea
                                                id="description"
                                                name="description"
                                                rows={3}
                                                value={data.description}
                                                onChange={(e) =>
                                                    setData("description", e.target.value)
                                                }
                                                className="mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                placeholder="Brief description of what this tag represents"
                                            />
                                            {errors.description && (
                                                <p className="mt-1 text-sm text-red-600">
                                                    {errors.description}
                                                </p>
                                            )}
                                        </div>

                                        <div className="col-span-6">
                                            <label className="block text-sm font-medium text-gray-700">
                                                Color
                                            </label>
                                            <div className="mt-2">
                                                <div className="flex items-center space-x-3">
                                                    <input
                                                        type="color"
                                                        value={data.color}
                                                        onChange={(e) =>
                                                            setData("color", e.target.value)
                                                        }
                                                        className="h-10 w-16 rounded-md border border-gray-300"
                                                    />
                                                    <input
                                                        type="text"
                                                        value={data.color}
                                                        onChange={(e) =>
                                                            setData("color", e.target.value)
                                                        }
                                                        className="block w-24 rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                        placeholder="#3B82F6"
                                                    />
                                                </div>
                                                <div className="mt-3 flex flex-wrap gap-2">
                                                    {colorPresets.map((color) => (
                                                        <button
                                                            key={color}
                                                            type="button"
                                                            onClick={() => setData("color", color)}
                                                            className={`h-8 w-8 rounded-full border-2 ${
                                                                data.color === color
                                                                    ? "border-gray-900"
                                                                    : "border-gray-300"
                                                            }`}
                                                            style={{ backgroundColor: color }}
                                                        />
                                                    ))}
                                                </div>
                                            </div>
                                            {errors.color && (
                                                <p className="mt-1 text-sm text-red-600">
                                                    {errors.color}
                                                </p>
                                            )}
                                        </div>

                                        <div className="col-span-6">
                                            <div className="flex items-center">
                                                <input
                                                    id="is_active"
                                                    name="is_active"
                                                    type="checkbox"
                                                    checked={data.is_active}
                                                    onChange={(e) =>
                                                        setData("is_active", e.target.checked)
                                                    }
                                                    className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                                />
                                                <label
                                                    htmlFor="is_active"
                                                    className="ml-2 block text-sm text-gray-900"
                                                >
                                                    Active
                                                </label>
                                            </div>
                                            <p className="mt-1 text-sm text-gray-500">
                                                Only active tags will be available for selection
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Submit Button */}
                        <div className="flex justify-end">
                            <button
                                type="button"
                                onClick={() => router.visit("/admin/blog-tags")}
                                className="mr-3 rounded-md border border-gray-300 bg-white py-2 px-4 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                disabled={processing}
                                className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50"
                            >
                                {processing ? "Updating..." : "Update Tag"}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </>
    );
};

export default Edit;
