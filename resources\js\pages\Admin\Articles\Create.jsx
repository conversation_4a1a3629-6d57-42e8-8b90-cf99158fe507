import { useState } from "react";
import { Head, useForm, router } from "@inertiajs/react";
import { ArrowLeftIcon } from "@phosphor-icons/react";
import TiptapEditor from "../../../components/TiptapEditor";
import DragDropImageUpload from "../../../components/DragDropImageUpload";

const Create = ({ blogTags = [], authors = [] }) => {
    const [imagePreview, setImagePreview] = useState(null);
    const { data, setData, post, processing, errors } = useForm({
        title: "",
        excerpt: "",
        blog_tag_id: "",
        author_id: "",
        content: "",
        read_time: "",
        image: null,
        is_featured: false,
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        post("/admin/articles", { forceFormData: true });
    };

    return (
        <>
            <Head title="Create Article" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center">
                    <button
                        onClick={() => router.visit("/admin/articles")}
                        className="mr-4 p-2 text-gray-400 hover:text-gray-600"
                    >
                        <ArrowLeftIcon className="h-5 w-5" />
                    </button>
                    <div>
                        <h1 className="text-2xl font-semibold text-gray-900">
                            Create Article
                        </h1>
                        <p className="mt-1 text-sm text-gray-600">
                            Add a new blog article
                        </p>
                    </div>
                </div>

                {/* Form */}
                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="bg-white px-4 py-5 shadow sm:rounded-lg sm:p-6">
                        <div className="md:grid md:grid-cols-3 md:gap-6">
                            <div className="md:col-span-1">
                                <h3 className="text-lg leading-6 font-medium text-gray-900">
                                    Article Information
                                </h3>
                                <p className="mt-1 text-sm text-gray-500">
                                    Basic information about the article
                                </p>
                            </div>
                            <div className="mt-5 md:col-span-2 md:mt-0">
                                <div className="grid grid-cols-6 gap-6">
                                    <div className="col-span-6">
                                        <label
                                            htmlFor="title"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Title
                                        </label>
                                        <input
                                            type="text"
                                            name="title"
                                            id="title"
                                            value={data.title}
                                            onChange={(e) =>
                                                setData("title", e.target.value)
                                            }
                                            className={`mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
                                                errors.title
                                                    ? "border-red-300"
                                                    : ""
                                            }`}
                                        />
                                        {errors.title && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors.title}
                                            </p>
                                        )}
                                    </div>

                                    <div className="col-span-6">
                                        <label
                                            htmlFor="excerpt"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Excerpt (Optional)
                                        </label>
                                        <textarea
                                            name="excerpt"
                                            id="excerpt"
                                            rows={3}
                                            value={data.excerpt}
                                            onChange={(e) =>
                                                setData(
                                                    "excerpt",
                                                    e.target.value,
                                                )
                                            }
                                            className={`mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
                                                errors.excerpt
                                                    ? "border-red-300"
                                                    : ""
                                            }`}
                                            placeholder="Brief excerpt or summary of the article..."
                                        />
                                        {errors.excerpt && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors.excerpt}
                                            </p>
                                        )}
                                    </div>

                                    <div className="col-span-6 sm:col-span-3">
                                        <label
                                            htmlFor="blog_tag_id"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Blog Tag
                                        </label>
                                        <select
                                            name="blog_tag_id"
                                            id="blog_tag_id"
                                            value={data.blog_tag_id}
                                            onChange={(e) =>
                                                setData(
                                                    "blog_tag_id",
                                                    e.target.value,
                                                )
                                            }
                                            className={`mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
                                                errors.blog_tag_id
                                                    ? "border-red-300"
                                                    : ""
                                            }`}
                                        >
                                            <option value="">
                                                Select a tag
                                            </option>
                                            {blogTags.map((tag) => (
                                                <option
                                                    key={tag.id}
                                                    value={tag.id}
                                                >
                                                    {tag.name}
                                                </option>
                                            ))}
                                        </select>
                                        {errors.blog_tag_id && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors.blog_tag_id}
                                            </p>
                                        )}
                                    </div>

                                    <div className="col-span-6 sm:col-span-3">
                                        <label
                                            htmlFor="read_time"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Read Time
                                        </label>
                                        <input
                                            type="text"
                                            name="read_time"
                                            id="read_time"
                                            placeholder="e.g., 5 min read"
                                            value={data.read_time}
                                            onChange={(e) =>
                                                setData(
                                                    "read_time",
                                                    e.target.value,
                                                )
                                            }
                                            className={`mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
                                                errors.read_time
                                                    ? "border-red-300"
                                                    : ""
                                            }`}
                                        />
                                        {errors.read_time && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors.read_time}
                                            </p>
                                        )}
                                    </div>

                                    <div className="col-span-6 sm:col-span-3">
                                        <div className="flex items-center">
                                            <input
                                                id="is_featured"
                                                name="is_featured"
                                                type="checkbox"
                                                checked={data.is_featured}
                                                onChange={(e) =>
                                                    setData(
                                                        "is_featured",
                                                        e.target.checked,
                                                    )
                                                }
                                                className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                            />
                                            <label
                                                htmlFor="is_featured"
                                                className="ml-2 block text-sm text-gray-900"
                                            >
                                                Feature in Discover Section
                                            </label>
                                        </div>
                                        <p className="mt-1 text-sm text-gray-500">
                                            Only 2 articles can be featured at a
                                            time
                                        </p>
                                    </div>

                                    {/* Author Information */}
                                    <div className="col-span-6 sm:col-span-3">
                                        <label
                                            htmlFor="author_id"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Author
                                        </label>
                                        <select
                                            name="author_id"
                                            id="author_id"
                                            value={data.author_id}
                                            onChange={(e) =>
                                                setData(
                                                    "author_id",
                                                    e.target.value,
                                                )
                                            }
                                            className={`mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
                                                errors.author_id
                                                    ? "border-red-300"
                                                    : ""
                                            }`}
                                        >
                                            <option value="">
                                                Select an author
                                            </option>
                                            {authors.map((author) => (
                                                <option
                                                    key={author.id}
                                                    value={author.id}
                                                >
                                                    {author.name}
                                                </option>
                                            ))}
                                        </select>
                                        {errors.author_id && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors.author_id}
                                            </p>
                                        )}
                                    </div>

                                    <div className="col-span-6">
                                        <label className="block text-sm font-medium text-gray-700">
                                            Featured Image
                                        </label>
                                        <div className="mt-1">
                                            <DragDropImageUpload
                                                onFileSelect={(file) => {
                                                    setData("image", file);
                                                    const reader =
                                                        new FileReader();
                                                    reader.onload = (e) =>
                                                        setImagePreview(
                                                            e.target.result,
                                                        );
                                                    reader.readAsDataURL(file);
                                                }}
                                                preview={imagePreview}
                                                onRemove={() => {
                                                    setImagePreview(null);
                                                    setData("image", null);
                                                }}
                                                label="Upload featured image"
                                            />
                                        </div>
                                        {errors.image && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors.image}
                                            </p>
                                        )}
                                    </div>

                                    {/* Content Editor */}
                                    <div className="col-span-6">
                                        <label className="mb-2 block text-sm font-medium text-gray-700">
                                            Content
                                        </label>
                                        <TiptapEditor
                                            value={data.content}
                                            onChange={(content) =>
                                                setData("content", content)
                                            }
                                            placeholder="Write your article content..."
                                            className="mb-12"
                                        />
                                        {errors.content && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors.content}
                                            </p>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Submit Button */}
                    <div className="flex justify-end">
                        <button
                            type="button"
                            onClick={() => router.visit("/admin/articles")}
                            className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled={processing}
                            className="ml-3 inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:opacity-50"
                        >
                            {processing ? "Creating..." : "Create Article"}
                        </button>
                    </div>
                </form>
            </div>
        </>
    );
};

export default Create;
