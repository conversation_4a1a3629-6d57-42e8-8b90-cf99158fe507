<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class ContactQuery extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'company_name',
        'phone_number',
        'email',
        'message',
    ];

    protected static function booted()
    {
        static::created(function ($contactQuery) {
            // Trigger real-time update notification
            $timestamp = now()->timestamp;
            Cache::put('contact_queries_updated', $timestamp, 300);
            Cache::put('contact_queries_last_created', $contactQuery->toArray(), 300);
        });

        static::updated(function ($contactQuery) {
            // Trigger real-time update notification
            $timestamp = now()->timestamp;
            Cache::put('contact_queries_updated', $timestamp, 300);
        });

        static::deleted(function ($contactQuery) {
            // Trigger real-time update notification
            $timestamp = now()->timestamp;
            Cache::put('contact_queries_updated', $timestamp, 300);
            Cache::put('contact_queries_last_deleted', $contactQuery->id, 300);
        });
    }
}
