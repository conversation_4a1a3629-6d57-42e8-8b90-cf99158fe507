import { useState, useEffect, useRef } from "react";
import { Head } from "@inertiajs/react";
import axios from "axios";
import {
    ArticleIcon,
    BriefcaseIcon,
    ChatCircleIcon,
    ClockIcon,
} from "@phosphor-icons/react";

const Dashboard = ({ stats }) => {
    const [recentQueries, setRecentQueries] = useState(
        stats.recent_queries || [],
    );
    const [newQueries, setNewQueries] = useState([]);
    const [lastCacheTimestamp, setLastCacheTimestamp] = useState(0);
    const [isListening, setIsListening] = useState(true);
    const [isUserInteracting, setIsUserInteracting] = useState(false);
    const [removingQueryId, setRemovingQueryId] = useState(null);
    const updateCheckIntervalRef = useRef(null);
    const interactionTimeoutRef = useRef(null);

    const statCards = [
        {
            name: "Total Articles",
            value: stats.articles,
            icon: ArticleIcon,
            color: "bg-blue-500",
        },
        {
            name: "Total Cases",
            value: stats.cases,
            icon: BriefcaseIcon,
            color: "bg-green-500",
        },
        {
            name: "Contact Queries",
            value: stats.contact_queries,
            icon: ChatCircleIcon,
            color: "bg-yellow-500",
        },
    ];

    // Check cache timestamp only (lightweight check)
    const checkCacheTimestamp = async () => {
        if (!isListening || isUserInteracting) {
            return;
        }

        try {
            const response = await axios.get(
                "/admin/contact-queries/cache-timestamp",
                {
                    headers: {
                        "X-Requested-With": "XMLHttpRequest",
                        Accept: "application/json",
                    },
                },
            );

            if (response.data.success) {
                const currentCacheTimestamp = response.data.cache_timestamp;

                // Only fetch new queries if cache timestamp has actually changed
                if (currentCacheTimestamp > lastCacheTimestamp) {
                    await fetchNewQueries();
                    setLastCacheTimestamp(currentCacheTimestamp);
                }
            }
        } catch (error) {
            console.error("Dashboard cache timestamp check error:", error);
        }
    };

    // Fetch new queries only when cache timestamp indicates database changes
    const fetchNewQueries = async () => {
        try {
            const response = await axios.get(
                "/admin/contact-queries/check-updates",
                {
                    params: { last_check: lastCacheTimestamp },
                    headers: {
                        "X-Requested-With": "XMLHttpRequest",
                        Accept: "application/json",
                    },
                },
            );

            if (response.data.success && response.data.has_updates) {
                // Get the latest 3 queries for dashboard
                const latestQueries = response.data.data.slice(0, 3);

                // Find truly new queries (not in current display)
                const existingIds = new Set([
                    ...newQueries.map((q) => q.id),
                    ...recentQueries.map((q) => q.id),
                ]);

                const uniqueNewQueries = latestQueries.filter(
                    (query) => !existingIds.has(query.id),
                );

                if (uniqueNewQueries.length > 0) {
                    setNewQueries((prev) => [...uniqueNewQueries, ...prev]);
                }
            }
        } catch (error) {
            console.error("Dashboard fetch new queries error:", error);
        }
    };

    // Handle user interaction detection
    const handleUserInteraction = () => {
        setIsUserInteracting(true);

        // Clear existing timeout
        if (interactionTimeoutRef.current) {
            clearTimeout(interactionTimeoutRef.current);
        }

        // Resume listening after 3 seconds of no interaction
        interactionTimeoutRef.current = setTimeout(() => {
            setIsUserInteracting(false);
        }, 3000);
    };

    // Set up database-driven update checking (independent of user interactions)
    useEffect(() => {
        if (isListening) {
            // Check immediately when component mounts or listening starts
            checkCacheTimestamp();
            // Then check every 10 seconds for cache timestamp changes (lightweight)
            updateCheckIntervalRef.current = setInterval(
                checkCacheTimestamp,
                10000,
            );
        }

        return () => {
            if (updateCheckIntervalRef.current) {
                clearInterval(updateCheckIntervalRef.current);
            }
        };
    }, [isListening]); // Removed isUserInteracting from dependencies

    // Pause listening when page is not visible
    useEffect(() => {
        const handleVisibilityChange = () => {
            if (document.hidden) {
                setIsListening(false);
            } else {
                setIsListening(true);
            }
        };

        document.addEventListener("visibilitychange", handleVisibilityChange);

        return () => {
            document.removeEventListener(
                "visibilitychange",
                handleVisibilityChange,
            );
        };
    }, []);

    // Add user interaction listeners
    useEffect(() => {
        const events = [
            "mousedown",
            "mousemove",
            "keypress",
            "scroll",
            "touchstart",
        ];

        events.forEach((event) => {
            document.addEventListener(event, handleUserInteraction, {
                passive: true,
            });
        });

        return () => {
            events.forEach((event) => {
                document.removeEventListener(event, handleUserInteraction);
            });

            if (interactionTimeoutRef.current) {
                clearTimeout(interactionTimeoutRef.current);
            }
        };
    }, []);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (updateCheckIntervalRef.current) {
                clearInterval(updateCheckIntervalRef.current);
            }
            if (interactionTimeoutRef.current) {
                clearTimeout(interactionTimeoutRef.current);
            }
        };
    }, []);

    // Combine and manage the 3-query limit with animations
    const displayQueries = [...newQueries, ...recentQueries].slice(0, 3);

    // Handle adding new queries with fade-out of oldest when needed
    useEffect(() => {
        if (newQueries.length > 0) {
            const totalQueries = newQueries.length + recentQueries.length;
            if (totalQueries > 3) {
                // Need to remove oldest queries
                const queriesToRemove = totalQueries - 3;
                const oldestQueries = recentQueries.slice(-queriesToRemove);

                // Start fade-out animation for oldest queries
                oldestQueries.forEach((query, index) => {
                    setTimeout(() => {
                        setRemovingQueryId(query.id);
                        setTimeout(() => {
                            setRecentQueries((prev) =>
                                prev.filter((q) => q.id !== query.id),
                            );
                            setRemovingQueryId(null);
                        }, 500); // Match fade-out duration
                    }, index * 100); // Stagger the removals
                });
            }

            // After a delay, move new queries to recent queries
            setTimeout(() => {
                setRecentQueries((prev) =>
                    [...newQueries, ...prev].slice(0, 3),
                );
                setNewQueries([]);
            }, 800); // After fade-in completes
        }
    }, [newQueries]);

    return (
        <>
            <Head title="Admin Dashboard" />
            <style jsx>{`
                .dashboard-query-fade-in {
                    animation: dashboardFadeInSlide 0.8s ease-out forwards;
                    opacity: 0;
                    transform: translateY(-10px);
                }

                .dashboard-query-fade-out {
                    animation: dashboardFadeOutSlide 0.5s ease-in forwards;
                    opacity: 1;
                    transform: translateY(0);
                }

                @keyframes dashboardFadeInSlide {
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                @keyframes dashboardFadeOutSlide {
                    to {
                        opacity: 0;
                        transform: translateY(10px);
                    }
                }

                .dashboard-new-query-highlight {
                    background: linear-gradient(
                        90deg,
                        rgba(59, 130, 246, 0.08) 0%,
                        rgba(59, 130, 246, 0.03) 100%
                    );
                    border-left: 2px solid #3b82f6;
                }
            `}</style>

            <div className="space-y-6">
                {/* Header */}
                <div>
                    <h1 className="text-2xl font-semibold text-gray-900">
                        Dashboard
                    </h1>
                    <p className="mt-1 text-sm text-gray-600">
                        Welcome to your admin panel. Here's an overview of your
                        content.
                    </p>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
                    {statCards.map((stat) => (
                        <div
                            key={stat.name}
                            className="relative overflow-hidden rounded-lg bg-white px-4 pt-5 pb-12 shadow sm:px-6 sm:pt-6"
                        >
                            <dt>
                                <div
                                    className={`absolute ${stat.color} rounded-md p-3`}
                                >
                                    <stat.icon
                                        className="h-6 w-6 text-white"
                                        aria-hidden="true"
                                    />
                                </div>
                                <p className="ml-16 truncate text-sm font-medium text-gray-500">
                                    {stat.name}
                                </p>
                            </dt>
                            <dd className="ml-16 flex items-baseline pb-6 sm:pb-7">
                                <p className="text-2xl font-semibold text-gray-900">
                                    {stat.value}
                                </p>
                            </dd>
                        </div>
                    ))}
                </div>

                {/* Recent Contact Queries */}
                <div className="rounded-lg bg-white shadow">
                    <div className="px-4 py-5 sm:p-6">
                        <div className="mb-4 flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                                <h3 className="text-lg leading-6 font-medium text-gray-900">
                                    Recent Contact Queries
                                </h3>
                                {newQueries.length > 0 && (
                                    <span className="inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">
                                        {newQueries.length} new
                                    </span>
                                )}
                            </div>
                            <div className="flex items-center space-x-3">
                                <button
                                    onClick={() => checkForUpdates()}
                                    className="text-xs text-gray-500 underline hover:text-gray-700"
                                    title="Check for new queries"
                                >
                                    🔄 Refresh
                                </button>
                                <a
                                    href="/admin/contact-queries"
                                    className="text-sm font-medium text-indigo-600 hover:text-indigo-500"
                                >
                                    View all →
                                </a>
                            </div>
                        </div>
                        {displayQueries.length > 0 ? (
                            <div className="flow-root">
                                <ul className="-my-5 divide-y divide-gray-200">
                                    {displayQueries.map((query) => {
                                        const isNewQuery = newQueries.some(
                                            (nq) => nq.id === query.id,
                                        );
                                        const isRemoving =
                                            removingQueryId === query.id;

                                        return (
                                            <li
                                                key={query.id}
                                                className={`py-4 ${
                                                    isNewQuery
                                                        ? "dashboard-query-fade-in dashboard-new-query-highlight"
                                                        : ""
                                                } ${
                                                    isRemoving
                                                        ? "dashboard-query-fade-out"
                                                        : ""
                                                }`}
                                            >
                                                <div className="flex items-center space-x-4">
                                                    <div className="flex-shrink-0">
                                                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200">
                                                            <span className="text-sm font-medium text-gray-700">
                                                                {query.name.charAt(
                                                                    0,
                                                                )}
                                                                {query.name
                                                                    .split(
                                                                        " ",
                                                                    )[1]
                                                                    ?.charAt(
                                                                        0,
                                                                    ) || ""}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div className="min-w-0 flex-1">
                                                        <p className="truncate text-sm font-medium text-gray-900">
                                                            {query.name}
                                                        </p>
                                                        <p className="truncate text-sm text-gray-500">
                                                            {query.email}
                                                        </p>
                                                        {query.company_name && (
                                                            <p className="truncate text-sm text-gray-500">
                                                                {
                                                                    query.company_name
                                                                }
                                                            </p>
                                                        )}
                                                    </div>
                                                    <div className="flex-shrink-0 text-sm text-gray-500">
                                                        <div className="flex items-center">
                                                            <ClockIcon className="mr-1 h-4 w-4" />
                                                            {new Date(
                                                                query.created_at,
                                                            ).toLocaleDateString()}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="mt-2">
                                                    <p className="line-clamp-2 text-sm text-gray-600">
                                                        {query.message}
                                                    </p>
                                                </div>
                                            </li>
                                        );
                                    })}
                                </ul>
                            </div>
                        ) : (
                            <p className="py-4 text-center text-gray-500">
                                No recent contact queries
                            </p>
                        )}
                    </div>
                </div>
            </div>
        </>
    );
};

export default Dashboard;
