<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\Article;
use App\Models\Author;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, add the author_id column
        Schema::table('articles', function (Blueprint $table) {
            $table->foreignId('author_id')->nullable()->constrained()->onDelete('cascade');
        });

        // Update existing articles to link to authors
        $articles = Article::whereNotNull('writer')->get();

        foreach ($articles as $article) {
            $writer = $article->writer;

            if (is_array($writer) && isset($writer['name'])) {
                $authorName = trim($writer['name']);

                // Find the corresponding author
                $author = Author::where('name', $authorName)->first();

                if ($author) {
                    $article->update(['author_id' => $author->id]);
                }
            }
        }

        // Remove the writer column
        Schema::table('articles', function (Blueprint $table) {
            $table->dropColumn('writer');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Add back the writer column
        Schema::table('articles', function (Blueprint $table) {
            $table->json('writer')->nullable();
        });

        // Restore writer data from authors
        $articles = Article::with('author')->get();

        foreach ($articles as $article) {
            if ($article->author) {
                $writerData = [
                    'name' => $article->author->name,
                    'avatar' => $article->author->image,
                    'bio' => '', // We don't have bio in the new structure
                ];

                $article->update(['writer' => $writerData]);
            }
        }

        // Remove the author_id column
        Schema::table('articles', function (Blueprint $table) {
            $table->dropForeign(['author_id']);
            $table->dropColumn('author_id');
        });
    }
};
