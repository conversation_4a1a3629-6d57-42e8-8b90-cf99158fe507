import { useState } from "react";
import { <PERSON>, <PERSON>, router } from "@inertiajs/react";
import {
    PlusIcon,
    PencilIcon,
    TrashIcon,
    UserIcon,
} from "@phosphor-icons/react";

const Index = ({ authors }) => {
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [authorToDelete, setAuthorToDelete] = useState(null);

    const handleDelete = (author) => {
        setAuthorToDelete(author);
        setShowDeleteModal(true);
    };

    const confirmDelete = () => {
        if (authorToDelete) {
            router.delete(`/admin/authors/${authorToDelete.id}`, {
                onSuccess: () => {
                    setShowDeleteModal(false);
                    setAuthorToDelete(null);
                },
                onError: (errors) => {
                    console.error("Delete error:", errors);
                    setShowDeleteModal(false);
                    setAuthorToDelete(null);
                    // You can add a toast notification here if needed
                },
            });
        }
    };

    return (
        <>
            <Head title="Authors" />

            <div className="px-4 sm:px-6 lg:px-8">
                <div className="sm:flex sm:items-center">
                    <div className="sm:flex-auto">
                        <h1 className="text-2xl leading-6 font-semibold text-gray-900">
                            Authors
                        </h1>
                        <p className="mt-2 text-sm text-gray-700">
                            Manage authors for your articles.
                        </p>
                    </div>
                    <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                        <Link
                            href="/admin/authors/create"
                            className="block rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                        >
                            <PlusIcon className="mr-1 inline-block h-4 w-4" />
                            Add Author
                        </Link>
                    </div>
                </div>

                <div className="mt-8 flow-root">
                    <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                        <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                            <div className="ring-opacity-5 overflow-hidden shadow ring-1 ring-black sm:rounded-lg">
                                <table className="min-w-full divide-y divide-gray-300">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th
                                                scope="col"
                                                className="py-3.5 pr-3 pl-4 text-left text-sm font-semibold text-gray-900 sm:pl-6"
                                            >
                                                Author
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                            >
                                                Articles
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                                            >
                                                Created
                                            </th>
                                            <th
                                                scope="col"
                                                className="relative py-3.5 pr-4 pl-3 sm:pr-6"
                                            >
                                                <span className="sr-only">
                                                    Actions
                                                </span>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200 bg-white">
                                        {authors.data.map((author) => (
                                            <tr key={author.id}>
                                                <td className="py-4 pr-3 pl-4 text-sm whitespace-nowrap sm:pl-6">
                                                    <div className="flex items-center">
                                                        <div className="h-10 w-10 flex-shrink-0">
                                                            {author.image ? (
                                                                <img
                                                                    className="h-10 w-10 rounded-full object-cover"
                                                                    src={
                                                                        author.image
                                                                    }
                                                                    alt={
                                                                        author.name
                                                                    }
                                                                />
                                                            ) : (
                                                                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-300">
                                                                    <UserIcon className="h-6 w-6 text-gray-600" />
                                                                </div>
                                                            )}
                                                        </div>
                                                        <div className="ml-4">
                                                            <div className="font-medium text-gray-900">
                                                                {author.name}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="px-3 py-4 text-sm whitespace-nowrap text-gray-500">
                                                    {author.articles_count}{" "}
                                                    articles
                                                </td>
                                                <td className="px-3 py-4 text-sm whitespace-nowrap text-gray-500">
                                                    {new Date(
                                                        author.created_at,
                                                    ).toLocaleDateString()}
                                                </td>
                                                <td className="relative py-4 pr-4 pl-3 text-right text-sm font-medium whitespace-nowrap sm:pr-6">
                                                    <div className="flex justify-end space-x-2">
                                                        <Link
                                                            href={`/admin/authors/${author.id}/edit`}
                                                            className="text-indigo-600 hover:text-indigo-900"
                                                        >
                                                            <PencilIcon className="h-4 w-4" />
                                                        </Link>
                                                        <button
                                                            onClick={() =>
                                                                handleDelete(
                                                                    author,
                                                                )
                                                            }
                                                            className="text-red-600 hover:text-red-900"
                                                            disabled={
                                                                author.articles_count >
                                                                0
                                                            }
                                                        >
                                                            <TrashIcon className="h-4 w-4" />
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Pagination */}
                {authors.links && (
                    <div className="mt-6 flex items-center justify-between">
                        <div className="flex flex-1 justify-between sm:hidden">
                            {authors.prev_page_url && (
                                <Link
                                    href={authors.prev_page_url}
                                    className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                                >
                                    Previous
                                </Link>
                            )}
                            {authors.next_page_url && (
                                <Link
                                    href={authors.next_page_url}
                                    className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                                >
                                    Next
                                </Link>
                            )}
                        </div>
                        <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                            <div>
                                <p className="text-sm text-gray-700">
                                    Showing{" "}
                                    <span className="font-medium">
                                        {authors.from}
                                    </span>{" "}
                                    to{" "}
                                    <span className="font-medium">
                                        {authors.to}
                                    </span>{" "}
                                    of{" "}
                                    <span className="font-medium">
                                        {authors.total}
                                    </span>{" "}
                                    results
                                </p>
                            </div>
                            <div>
                                <nav
                                    className="isolate inline-flex -space-x-px rounded-md shadow-sm"
                                    aria-label="Pagination"
                                >
                                    {authors.links.map((link, index) => (
                                        <Link
                                            key={index}
                                            href={link.url || "#"}
                                            className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${
                                                link.active
                                                    ? "z-10 bg-indigo-600 text-white focus-visible:outline focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                                                    : "text-gray-900 ring-1 ring-gray-300 ring-inset hover:bg-gray-50 focus:outline-offset-0"
                                            } ${
                                                index === 0
                                                    ? "rounded-l-md"
                                                    : index ===
                                                        authors.links.length - 1
                                                      ? "rounded-r-md"
                                                      : ""
                                            }`}
                                            dangerouslySetInnerHTML={{
                                                __html: link.label,
                                            }}
                                        />
                                    ))}
                                </nav>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Delete Modal */}
            {showDeleteModal && (
                <div className="fixed inset-0 z-50 overflow-y-auto">
                    <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                        <div className="bg-opacity-75 fixed inset-0 bg-gray-500 transition-opacity" />
                        <div className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg">
                            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                                <div className="sm:flex sm:items-start">
                                    <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                        <TrashIcon className="h-6 w-6 text-red-600" />
                                    </div>
                                    <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                        <h3 className="text-base leading-6 font-semibold text-gray-900">
                                            Delete Author
                                        </h3>
                                        <div className="mt-2">
                                            <p className="text-sm text-gray-500">
                                                Are you sure you want to delete
                                                "{authorToDelete?.name}"? This
                                                action cannot be undone.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                                <button
                                    type="button"
                                    className="inline-flex w-full justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 sm:ml-3 sm:w-auto"
                                    onClick={confirmDelete}
                                >
                                    Delete
                                </button>
                                <button
                                    type="button"
                                    className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-gray-300 ring-inset hover:bg-gray-50 sm:mt-0 sm:w-auto"
                                    onClick={() => setShowDeleteModal(false)}
                                >
                                    Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default Index;
