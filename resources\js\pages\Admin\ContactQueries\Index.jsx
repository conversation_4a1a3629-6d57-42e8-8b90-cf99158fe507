import { useState, useEffect, useRef } from "react";
import { Head, router, useForm } from "@inertiajs/react";
import axios from "axios";
import {
    MagnifyingGlassIcon,
    FileArrowDownIcon,
    TrashIcon,
    CalendarIcon,
    EnvelopeIcon,
    BuildingOfficeIcon,
    PhoneIcon,
} from "@phosphor-icons/react";

const Index = ({ contactQueries, filters }) => {
    const [selectedQueries, setSelectedQueries] = useState([]);
    const [newQueries, setNewQueries] = useState([]);
    const [readNewQueries, setReadNewQueries] = useState([]);
    const [lastCacheTimestamp, setLastCacheTimestamp] = useState(0);
    const [isListening, setIsListening] = useState(true);
    const [isUserInteracting, setIsUserInteracting] = useState(false);
    const [pollingError, setPollingError] = useState(null);
    const cacheCheckIntervalRef = useRef(null);
    const interactionTimeoutRef = useRef(null);

    const { data, setData, get, processing } = useForm({
        search: filters.search || "",
        date_from: filters.date_from || "",
        date_to: filters.date_to || "",
    });

    // Check cache timestamp only (lightweight check)
    const checkCacheTimestamp = async () => {
        if (!isListening || isUserInteracting) {
            return;
        }

        try {
            const response = await axios.get(
                "/admin/contact-queries/cache-timestamp",
                {
                    headers: {
                        "X-Requested-With": "XMLHttpRequest",
                        Accept: "application/json",
                    },
                },
            );

            if (response.data.success) {
                const currentCacheTimestamp = response.data.cache_timestamp;

                // Only fetch new queries if cache timestamp has actually changed
                if (currentCacheTimestamp > lastCacheTimestamp) {
                    await fetchNewQueries(currentCacheTimestamp);
                    setLastCacheTimestamp(currentCacheTimestamp);
                }
                setPollingError(null);
            }
        } catch (error) {
            console.error("Cache timestamp check error:", error);
            setPollingError("Failed to check for updates");
        }
    };

    // Fetch new queries only when cache timestamp indicates database changes
    const fetchNewQueries = async (cacheTimestamp) => {
        try {
            const response = await axios.get(
                "/admin/contact-queries/check-updates",
                {
                    params: { last_check: lastCacheTimestamp },
                    headers: {
                        "X-Requested-With": "XMLHttpRequest",
                        Accept: "application/json",
                    },
                },
            );

            if (response.data.success && response.data.has_updates) {
                // Get new queries that aren't already displayed
                const existingIds = new Set([
                    ...newQueries.map((q) => q.id),
                    ...readNewQueries.map((q) => q.id),
                    ...(contactQueries.data || []).map((q) => q.id),
                ]);

                const uniqueNewQueries = response.data.data.filter(
                    (query) => !existingIds.has(query.id),
                );

                if (uniqueNewQueries.length > 0) {
                    setNewQueries((prev) => [...uniqueNewQueries, ...prev]);
                }
            }
        } catch (error) {
            console.error("Fetch new queries error:", error);
            setPollingError("Failed to fetch new queries");
        }
    };

    // Manual refresh function for the refresh button
    const manualRefresh = async () => {
        setPollingError(null);
        await checkCacheTimestamp();
    };

    // Handle user interaction detection
    const handleUserInteraction = () => {
        setIsUserInteracting(true);

        // Clear existing timeout
        if (interactionTimeoutRef.current) {
            clearTimeout(interactionTimeoutRef.current);
        }

        // Resume listening after 3 seconds of no interaction
        interactionTimeoutRef.current = setTimeout(() => {
            setIsUserInteracting(false);
        }, 3000);
    };

    // Set up database-driven update checking (independent of user interactions)
    useEffect(() => {
        if (isListening) {
            // Check immediately when component mounts or listening starts
            checkCacheTimestamp();
            // Then check every 10 seconds for cache timestamp changes (lightweight)
            cacheCheckIntervalRef.current = setInterval(
                checkCacheTimestamp,
                10000,
            );
        }

        return () => {
            if (cacheCheckIntervalRef.current) {
                clearInterval(cacheCheckIntervalRef.current);
            }
        };
    }, [isListening]); // Removed isUserInteracting from dependencies

    // Pause listening when page is not visible
    useEffect(() => {
        const handleVisibilityChange = () => {
            if (document.hidden) {
                setIsListening(false);
            } else {
                setIsListening(true);
            }
        };

        document.addEventListener("visibilitychange", handleVisibilityChange);

        return () => {
            document.removeEventListener(
                "visibilitychange",
                handleVisibilityChange,
            );
        };
    }, []);

    // Add user interaction listeners
    useEffect(() => {
        const events = [
            "mousedown",
            "mousemove",
            "keypress",
            "scroll",
            "touchstart",
        ];

        events.forEach((event) => {
            document.addEventListener(event, handleUserInteraction, {
                passive: true,
            });
        });

        return () => {
            events.forEach((event) => {
                document.removeEventListener(event, handleUserInteraction);
            });

            if (interactionTimeoutRef.current) {
                clearTimeout(interactionTimeoutRef.current);
            }
        };
    }, []);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (cacheCheckIntervalRef.current) {
                clearInterval(cacheCheckIntervalRef.current);
            }
            if (interactionTimeoutRef.current) {
                clearTimeout(interactionTimeoutRef.current);
            }
        };
    }, []);

    const handleSearch = (e) => {
        e.preventDefault();
        // Clear new queries when searching as they might not match the filter
        setNewQueries([]);
        setReadNewQueries([]);
        setLastCacheTimestamp(0);
        get("/admin/contact-queries", {
            preserveState: true,
            preserveScroll: true,
        });
    };

    // Function to mark new queries as read (removes highlighting but keeps queries visible)
    const markNewQueriesAsRead = () => {
        if (newQueries.length > 0) {
            // Move new queries to read queries to keep them visible
            setReadNewQueries((prev) => [...prev, ...newQueries]);
            // Clear the newQueries array so they lose their "new" styling
            setNewQueries([]);
        }
    };

    const handleDelete = (query) => {
        if (confirm("Are you sure you want to delete this contact query?")) {
            router.delete(`/admin/contact-queries/${query.id}`);
        }
    };

    const handleBulkDelete = () => {
        if (selectedQueries.length === 0) return;

        if (
            confirm(
                `Are you sure you want to delete ${selectedQueries.length} selected queries?`,
            )
        ) {
            router.delete("/admin/contact-queries", {
                data: { ids: selectedQueries },
                onSuccess: () => setSelectedQueries([]),
            });
        }
    };

    const toggleSelectAll = () => {
        if (selectedQueries.length === allQueries.length) {
            setSelectedQueries([]);
        } else {
            setSelectedQueries(allQueries.map((query) => query.id));
        }
    };

    const toggleSelect = (queryId) => {
        if (selectedQueries.includes(queryId)) {
            setSelectedQueries(selectedQueries.filter((id) => id !== queryId));
        } else {
            setSelectedQueries([...selectedQueries, queryId]);
        }
    };

    // Combine all queries with proper deduplication
    // Create a Map to track unique queries by ID, with priority: newQueries > readNewQueries > contactQueries.data
    const queryMap = new Map();

    // First add original queries (lowest priority)
    (contactQueries.data || []).forEach((query) => {
        queryMap.set(query.id, { ...query, isNew: false, isReadNew: false });
    });

    // Then add read new queries (medium priority)
    readNewQueries.forEach((query) => {
        queryMap.set(query.id, { ...query, isNew: false, isReadNew: true });
    });

    // Finally add new queries (highest priority)
    newQueries.forEach((query) => {
        queryMap.set(query.id, { ...query, isNew: true, isReadNew: false });
    });

    // Convert back to array, sorted by creation date (newest first)
    const allQueries = Array.from(queryMap.values()).sort(
        (a, b) => new Date(b.created_at) - new Date(a.created_at),
    );

    // Count unread new queries (only those in newQueries)
    const unreadNewQueries = newQueries;

    return (
        <>
            <Head title="Contact Queries" />
            <style jsx>{`
                .fade-in-query {
                    animation: fadeInSlide 0.8s ease-out forwards;
                    opacity: 0;
                    transform: translateY(-10px);
                }

                @keyframes fadeInSlide {
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .new-query-highlight {
                    background: linear-gradient(
                        90deg,
                        rgba(59, 130, 246, 0.1) 0%,
                        rgba(59, 130, 246, 0.05) 100%
                    );
                    border-left: 3px solid #3b82f6;
                }
            `}</style>

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-semibold text-gray-900">
                            Contact Queries
                        </h1>
                        <p className="mt-1 text-sm text-gray-600">
                            Manage contact form submissions
                        </p>
                    </div>
                    <div className="flex space-x-3">
                        {selectedQueries.length > 0 && (
                            <button
                                onClick={handleBulkDelete}
                                className="inline-flex items-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700"
                            >
                                <TrashIcon className="mr-2 h-4 w-4" />
                                Delete Selected ({selectedQueries.length})
                            </button>
                        )}
                        <button
                            onClick={manualRefresh}
                            className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                            title="Check for new queries"
                        >
                            🔄 Refresh
                        </button>
                        <a
                            href="/admin/contact-queries/export"
                            className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                        >
                            <FileArrowDownIcon className="mr-2 h-4 w-4" />
                            Export Excel
                        </a>
                    </div>
                </div>

                {/* Search and Filters */}
                <div className="rounded-lg bg-white p-6 shadow">
                    <form
                        onSubmit={handleSearch}
                        className="grid grid-cols-1 gap-4 sm:grid-cols-4"
                    >
                        <div className="sm:col-span-2">
                            <label
                                htmlFor="search"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Search
                            </label>
                            <div className="relative mt-1 rounded-md shadow-sm">
                                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                                </div>
                                <input
                                    type="text"
                                    name="search"
                                    id="search"
                                    value={data.search}
                                    onChange={(e) =>
                                        setData("search", e.target.value)
                                    }
                                    className="block w-full rounded-md border-gray-300 p-2 pl-10 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                    placeholder="Search by name, email, company..."
                                />
                            </div>
                        </div>
                        <div>
                            <label
                                htmlFor="date_from"
                                className="block text-sm font-medium text-gray-700"
                            >
                                From Date
                            </label>
                            <input
                                type="date"
                                name="date_from"
                                id="date_from"
                                value={data.date_from}
                                onChange={(e) =>
                                    setData("date_from", e.target.value)
                                }
                                className="mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            />
                        </div>
                        <div>
                            <label
                                htmlFor="date_to"
                                className="block text-sm font-medium text-gray-700"
                            >
                                To Date
                            </label>
                            <input
                                type="date"
                                name="date_to"
                                id="date_to"
                                value={data.date_to}
                                onChange={(e) =>
                                    setData("date_to", e.target.value)
                                }
                                className="mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            />
                        </div>
                        <div className="flex justify-end space-x-3 sm:col-span-4">
                            <button
                                type="button"
                                onClick={() => {
                                    setData({
                                        search: "",
                                        date_from: "",
                                        date_to: "",
                                    });
                                    router.get("/admin/contact-queries");
                                }}
                                className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
                            >
                                Clear
                            </button>
                            <button
                                type="submit"
                                disabled={processing}
                                className="rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 disabled:opacity-50"
                            >
                                {processing ? "Searching..." : "Search"}
                            </button>
                        </div>
                    </form>
                </div>

                {/* Contact Queries Table */}
                <div className="overflow-hidden bg-white shadow sm:rounded-md">
                    {allQueries.length > 0 ? (
                        <>
                            <div className="border-b border-gray-200 px-4 py-3">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                        <input
                                            type="checkbox"
                                            checked={
                                                selectedQueries.length ===
                                                allQueries.length
                                            }
                                            onChange={toggleSelectAll}
                                            className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                        />
                                        <label className="ml-2 text-sm text-gray-700">
                                            Select all ({allQueries.length})
                                        </label>
                                    </div>
                                    {unreadNewQueries.length > 0 && (
                                        <div className="flex items-center space-x-3">
                                            <div className="text-sm font-medium text-blue-600">
                                                {unreadNewQueries.length} new{" "}
                                                {unreadNewQueries.length === 1
                                                    ? "query"
                                                    : "queries"}
                                            </div>
                                            <button
                                                onClick={markNewQueriesAsRead}
                                                className="text-xs text-gray-500 underline hover:text-gray-700"
                                            >
                                                Mark as read
                                            </button>
                                        </div>
                                    )}
                                    {pollingError && (
                                        <div className="flex items-center space-x-2 text-sm text-red-600">
                                            <span>⚠️ {pollingError}</span>
                                            <button
                                                onClick={() => {
                                                    setPollingError(null);
                                                    checkForUpdates();
                                                }}
                                                className="text-xs underline hover:text-red-700"
                                            >
                                                Retry
                                            </button>
                                        </div>
                                    )}
                                </div>
                            </div>
                            <ul className="divide-y divide-gray-200">
                                {allQueries.map((query) => {
                                    return (
                                        <li
                                            key={query.id}
                                            className={`px-4 py-4 ${query.isNew ? "fade-in-query new-query-highlight" : ""}`}
                                        >
                                            <div className="flex items-start space-x-4">
                                                <div className="flex-shrink-0 pt-1">
                                                    <input
                                                        type="checkbox"
                                                        checked={selectedQueries.includes(
                                                            query.id,
                                                        )}
                                                        onChange={() =>
                                                            toggleSelect(
                                                                query.id,
                                                            )
                                                        }
                                                        className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                                    />
                                                </div>
                                                <div className="min-w-0 flex-1">
                                                    <div className="flex items-center justify-between">
                                                        <div className="flex items-center space-x-3">
                                                            <div className="flex-shrink-0">
                                                                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200">
                                                                    <span className="text-sm font-medium text-gray-700">
                                                                        {query.name.charAt(
                                                                            0,
                                                                        )}
                                                                        {query.name
                                                                            .split(
                                                                                " ",
                                                                            )[1]
                                                                            ?.charAt(
                                                                                0,
                                                                            ) ||
                                                                            ""}
                                                                    </span>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <p className="text-sm font-medium text-gray-900">
                                                                    {query.name}
                                                                </p>
                                                                <div className="flex items-center space-x-4 text-sm text-gray-500">
                                                                    <div className="flex items-center">
                                                                        <EnvelopeIcon className="mr-1 h-4 w-4" />
                                                                        {
                                                                            query.email
                                                                        }
                                                                    </div>
                                                                    {query.phone_number && (
                                                                        <div className="flex items-center">
                                                                            <PhoneIcon className="mr-1 h-4 w-4" />
                                                                            {
                                                                                query.phone_number
                                                                            }
                                                                        </div>
                                                                    )}
                                                                    {query.company_name && (
                                                                        <div className="flex items-center">
                                                                            <BuildingOfficeIcon className="mr-1 h-4 w-4" />
                                                                            {
                                                                                query.company_name
                                                                            }
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="flex items-center space-x-2">
                                                            <div className="flex items-center text-sm text-gray-500">
                                                                <CalendarIcon className="mr-1 h-4 w-4" />
                                                                {new Date(
                                                                    query.created_at,
                                                                ).toLocaleDateString()}
                                                            </div>
                                                            <button
                                                                onClick={() =>
                                                                    handleDelete(
                                                                        query,
                                                                    )
                                                                }
                                                                className="text-red-600 hover:text-red-900"
                                                                title="Delete Query"
                                                            >
                                                                <TrashIcon className="h-4 w-4" />
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div className="mt-2">
                                                        <p className="text-sm text-gray-600">
                                                            <span className="font-medium">
                                                                Message:
                                                            </span>{" "}
                                                            {query.message}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    );
                                })}
                            </ul>
                        </>
                    ) : (
                        <div className="py-12 text-center">
                            <p className="text-gray-500">
                                No contact queries found
                            </p>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
};

export default Index;
