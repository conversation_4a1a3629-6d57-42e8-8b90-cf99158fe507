import { XIcon } from "@phosphor-icons/react";
import { useForm } from "@inertiajs/react";
import { useEffect } from "react";
import toast from "react-hot-toast";

export default function ContactModal({ open, onClose }) {
    const { data, setData, post, processing, reset } = useForm({
        name: "",
        company_name: "",
        phone_number: "",
        email: "",
        message: "",
    });

    useEffect(() => {
        if (open) {
            document.body.style.overflow = "hidden";
        } else {
            document.body.style.overflow = "";
        }
        return () => {
            document.body.style.overflow = "";
        };
    }, [open]);

    if (!open) return null;

    const handleSubmit = (e) => {
        e.preventDefault();
        post("/contact", {
            onSuccess: () => {
                reset();
                onClose();
                toast.success(
                    "Message sent successfully!\nWe will respond shortly!",
                );
            },
        });
    };

    return (
        <div className="fixed inset-0 z-[100] flex items-center justify-center">
            {/* Backdrop */}
            <div
                className="fixed inset-0 bg-black/40 backdrop-blur-[6px] transition-opacity"
                aria-hidden="true"
                onClick={onClose}
            />
            {/* Modal */}
            <div
                className="relative z-10 mx-auto flex h-fit w-full max-w-lg flex-col gap-8 rounded-3xl border-10 border-[#F5F6F9] bg-white p-8 shadow-2xl md:p-10"
                onClick={(e) => e.stopPropagation()}
                role="dialog"
                aria-modal="true"
            >
                {/* Close Button */}
                <button
                    type="button"
                    onClick={onClose}
                    className="absolute top-1 right-1 flex size-8 cursor-pointer items-center justify-center rounded-full bg-gray-500 hover:bg-gray-400 focus:ring-2 focus:ring-black/20 focus:outline-none"
                    aria-label="Close"
                >
                    <XIcon size={24} weight="bold" className="text-white" />
                </button>
                <form
                    className="flex flex-col gap-2"
                    onSubmit={handleSubmit}
                    autoComplete="off"
                >
                    {/* Name */}
                    <div>
                        <label className="mb-2 block text-sm font-normal text-black">
                            Name
                        </label>
                        <input
                            type="text"
                            value={data.name || ""}
                            onChange={(e) => setData("name", e.target.value)}
                            className="w-full rounded-xl border-none bg-gray-100 px-4 py-2 text-sm text-gray-900 outline-none placeholder:text-gray-400 focus:ring-2 focus:ring-[rgb(255,108,10)] focus:outline-none"
                            placeholder="Panda Patronage"
                            name="name"
                            autoComplete="name"
                            autoFocus
                            required
                        />
                    </div>
                    {/* Company Name & Phone Number */}
                    <div className="flex gap-4">
                        <div className="flex-1">
                            <label className="mb-2 block text-sm font-normal text-black">
                                Company Name
                            </label>
                            <input
                                type="text"
                                value={data.company_name || ""}
                                onChange={(e) =>
                                    setData("company_name", e.target.value)
                                }
                                className="w-full rounded-xl border-none bg-gray-100 px-4 py-2 text-sm text-gray-900 outline-none placeholder:text-gray-400 focus:ring-2 focus:ring-[rgb(255,108,10)] focus:outline-none"
                                placeholder="Panda Patronage"
                                name="company_name"
                                autoComplete="organization"
                                required
                            />
                        </div>
                        <div className="flex-1">
                            <label className="mb-2 block text-sm font-normal text-black">
                                Phone Number
                            </label>
                            <input
                                type="text"
                                value={data.phone_number || ""}
                                onChange={(e) =>
                                    setData("phone_number", e.target.value)
                                }
                                className="w-full rounded-xl border-none bg-gray-100 px-4 py-2 text-sm text-gray-900 outline-none placeholder:text-gray-400 focus:ring-2 focus:ring-[rgb(255,108,10)] focus:outline-none"
                                placeholder="+92 319 0008924"
                                name="phone_number"
                                autoComplete="tel"
                                required
                            />
                        </div>
                    </div>
                    {/* Email Address */}
                    <div>
                        <label className="mb-2 block text-sm font-normal text-black">
                            Email Address
                        </label>
                        <input
                            type="email"
                            value={data.email || ""}
                            onChange={(e) => setData("email", e.target.value)}
                            className="w-full rounded-xl border-none bg-gray-100 px-4 py-2 text-sm text-gray-900 outline-none placeholder:text-gray-400 focus:ring-2 focus:ring-[rgb(255,108,10)] focus:outline-none"
                            placeholder="<EMAIL>"
                            name="email"
                            autoComplete="email"
                            required
                        />
                    </div>
                    {/* Description */}
                    <div>
                        <label className="mb-2 block text-sm font-normal text-black">
                            Description
                        </label>
                        <textarea
                            value={data.message}
                            onChange={(e) => setData("message", e.target.value)}
                            className="min-h-[120px] w-full resize-none rounded-xl border-none bg-gray-100 px-4 py-2 text-sm outline-none placeholder:text-gray-400 focus:ring-2 focus:ring-[rgb(255,108,10)] focus:outline-none"
                            placeholder="How may we best assist you?"
                            name="message"
                            required
                        />
                    </div>
                    {/* Button */}
                    <button
                        type="submit"
                        disabled={processing}
                        className="mt-2 w-full cursor-pointer rounded-xl bg-black py-2 text-sm font-light text-white shadow-md transition hover:bg-gray-900 focus:ring-2 focus:ring-orange-400/40 focus:outline-none disabled:opacity-60"
                    >
                        Send message
                    </button>
                </form>
            </div>
        </div>
    );
}
