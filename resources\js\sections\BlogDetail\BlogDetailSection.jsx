import ScrollReveal from "../../components/ScrollReveal";
import { Link } from "@inertiajs/react";
import {
    ArrowLeftIcon,
    CalendarBlankIcon,
    ClockIcon,
    TagIcon,
} from "@phosphor-icons/react";

export default function BlogDetailSection({ article }) {
    if (!article) return null;
    const { image, date, title, content, author, read_time, blog_tag } =
        article;
    const tagName = blog_tag?.name || "Blog";
    return (
        <section className="flex w-full flex-col items-center px-4 py-12">
            <div className="w-full max-w-3xl">
                <ScrollReveal>
                    <Link
                        href="/blog"
                        className="mb-6 flex items-start text-sm font-medium text-gray-900"
                    >
                        <ArrowLeftIcon
                            className="mr-2 inline-block"
                            size={16}
                            weight="thin"
                        />{" "}
                        All articles
                    </Link>
                    <h1 className="tablet:text-5xl mb-4 text-4xl leading-tight font-bold text-gray-900">
                        {title}
                    </h1>
                </ScrollReveal>
                <ScrollReveal className="tablet:grid-cols-[fit-content(100%)_fit-content(100%)_fit-content(100%)_fit-content(100%)] mb-8 grid w-full grid-cols-[fit-content(100%)_fit-content(100%)] items-center justify-start gap-x-8 gap-y-3 rounded-xl bg-gray-50 px-6 py-5">
                    <div className="flex w-full items-center gap-2">
                        {author?.image ? (
                            <img
                                src={author.image}
                                alt={author.name}
                                className="h-6 w-6 rounded-full object-cover"
                                onError={(e) => {
                                    e.target.style.display = "none";
                                }}
                            />
                        ) : (
                            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-300">
                                <span className="text-xs text-gray-600">
                                    {author?.name?.charAt(0)?.toUpperCase() ||
                                        "?"}
                                </span>
                            </div>
                        )}
                        <span className="text-sm font-medium text-gray-900">
                            {author?.name || "Unknown Author"}
                        </span>
                    </div>
                    <div className="flex w-full items-center gap-2 text-sm text-gray-600">
                        <ClockIcon
                            weight="duotone"
                            size={20}
                            className="inline-block"
                        />
                        {read_time}
                    </div>
                    <div className="flex w-full items-center gap-2 text-sm text-gray-600">
                        <CalendarBlankIcon
                            weight="duotone"
                            size={20}
                            className="inline-block"
                        />
                        {date}
                    </div>
                    <div className="flex w-full items-center gap-2 text-sm text-gray-600">
                        <TagIcon
                            weight="duotone"
                            size={20}
                            className="inline-block"
                        />
                        {tagName}
                    </div>
                </ScrollReveal>
                <ScrollReveal>
                    <img
                        src={image}
                        alt={title}
                        className="mx-auto mb-10 h-[480px] w-full max-w-[762px] rounded-[12px] object-cover"
                        style={{ aspectRatio: "762/480" }}
                    />
                </ScrollReveal>
                <ScrollReveal className="space-y-4 text-base leading-relaxed text-gray-700">
                    {(() => {
                        // Handle different content formats
                        if (!content) {
                            return <p>No content available.</p>;
                        }

                        // If content is a string, handle both HTML and plain text
                        if (typeof content === "string") {
                            // Check if it's HTML content (from TiptapEditor)
                            if (
                                content.includes("<") &&
                                content.includes(">")
                            ) {
                                return (
                                    <div
                                        key="html-content"
                                        dangerouslySetInnerHTML={{
                                            __html: content,
                                        }}
                                        className="prose prose-sm max-w-none"
                                    />
                                );
                            }
                            // Handle plain text content
                            return content
                                .split("\n\n")
                                .map((paragraph, i) => (
                                    <p key={i}>{paragraph.trim()}</p>
                                ));
                        }

                        // If content is not an array, try to handle it gracefully
                        if (!Array.isArray(content)) {
                            return <p>Content format not supported.</p>;
                        }

                        // Handle array content (both old and new formats)
                        return content.map((block, i) => {
                            // Handle both old format (string) and new format (object with type and content)
                            if (typeof block === "string") {
                                return <p key={i}>{block}</p>;
                            }

                            // Handle new structured content format
                            switch (block.type) {
                                case "paragraph":
                                    return <p key={i}>{block.content}</p>;
                                case "heading":
                                    return (
                                        <h2
                                            key={i}
                                            className="mt-8 mb-4 text-2xl font-bold text-gray-900"
                                        >
                                            {block.content}
                                        </h2>
                                    );
                                case "list":
                                    const listItems = block.content
                                        .split("\n")
                                        .filter((item) => item.trim());
                                    return (
                                        <ul
                                            key={i}
                                            className="list-inside list-disc space-y-2"
                                        >
                                            {listItems.map((item, idx) => (
                                                <li key={idx}>{item.trim()}</li>
                                            ))}
                                        </ul>
                                    );
                                case "quote":
                                    return (
                                        <blockquote
                                            key={i}
                                            className="my-6 border-l-4 border-gray-300 pl-4 text-gray-600 italic"
                                        >
                                            {block.content}
                                        </blockquote>
                                    );
                                default:
                                    return (
                                        <p key={i}>{block.content || block}</p>
                                    );
                            }
                        });
                    })()}
                </ScrollReveal>
            </div>
        </section>
    );
}
