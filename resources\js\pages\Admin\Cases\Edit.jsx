import { useState } from "react";
import { Head, useForm, router } from "@inertiajs/react";
import { ArrowLeftIcon } from "@phosphor-icons/react";
import DragDropImageUpload from "../../../components/DragDropImageUpload";

const Edit = ({ case: portfolioCase, portfolioTags = [] }) => {
    const [logoPreview, setLogoPreview] = useState(portfolioCase.logo);
    const [imagePreviews, setImagePreviews] = useState(
        portfolioCase.images || [],
    );

    const { data, setData, post, processing, errors } = useForm({
        title: portfolioCase.title || "",
        company_name: portfolioCase.company_name || "",
        portfolio_tag_id: portfolioCase.portfolio_tag_id || "",
        logo: null,
        images: [],
        existing_images: portfolioCase.images || [],
        is_published: portfolioCase.is_published ?? true,
        _method: "PUT",
    });

    const handleSubmit = (e) => {
        e.preventDefault();

        // Client-side validation for new images
        if (data.images && data.images.length > 0) {
            const maxSize = 2048 * 1024; // 2MB in bytes
            const oversizedImages = [];
            data.images.forEach((image, index) => {
                if (image.size > maxSize) {
                    oversizedImages.push({
                        index: index + 1,
                        name: image.name,
                        size:
                            Math.round((image.size / 1024 / 1024) * 100) / 100, // Size in MB
                    });
                }
            });

            if (oversizedImages.length > 0) {
                const errorMessage = oversizedImages
                    .map(
                        (img) =>
                            `New Image ${img.index} (${img.name}): ${img.size}MB`,
                    )
                    .join("\n");
                alert(
                    `The following new images exceed the 2MB limit:\n\n${errorMessage}\n\nPlease resize or replace these images.`,
                );
                return;
            }
        }

        post(`/admin/cases/${portfolioCase.slug}`, data, {
            forceFormData: true,
            onError: (errors) => {
                console.error("Validation errors:", errors);
                // Check for specific image size errors
                const imageErrors = Object.keys(errors).filter((key) =>
                    key.startsWith("images."),
                );
                if (imageErrors.length > 0) {
                    console.error(
                        "Individual image errors found:",
                        imageErrors.map((key) => `${key}: ${errors[key]}`),
                    );
                }
            },
        });
    };

    return (
        <>
            <Head title={`Edit Case - ${portfolioCase.title}`} />

            <div className="min-h-screen bg-gray-50 py-6">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                <button
                                    onClick={() => router.visit("/admin/cases")}
                                    className="flex items-center text-gray-600 hover:text-gray-900"
                                >
                                    <ArrowLeftIcon className="mr-2 h-5 w-5" />
                                    Back to Cases
                                </button>
                            </div>
                        </div>
                        <div className="mt-4">
                            <h1 className="text-3xl font-bold text-gray-900">
                                Edit Case
                            </h1>
                            <p className="mt-2 text-gray-600">
                                Update the portfolio case information and
                                images.
                            </p>
                        </div>
                    </div>

                    {/* Form */}
                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="bg-white px-4 py-5 shadow sm:rounded-lg sm:p-6">
                            <div className="md:grid md:grid-cols-3 md:gap-6">
                                <div className="md:col-span-1">
                                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                                        Case Information
                                    </h3>
                                    <p className="mt-1 text-sm text-gray-500">
                                        Basic information about the portfolio
                                        case
                                    </p>
                                </div>
                                <div className="mt-5 md:col-span-2 md:mt-0">
                                    <div className="grid grid-cols-6 gap-6">
                                        <div className="col-span-6">
                                            <label
                                                htmlFor="title"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Title
                                            </label>
                                            <input
                                                type="text"
                                                name="title"
                                                id="title"
                                                value={data.title}
                                                onChange={(e) =>
                                                    setData(
                                                        "title",
                                                        e.target.value,
                                                    )
                                                }
                                                className={`mt-1 block w-full rounded-md p-2 border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
                                                    errors.title 
                                                        ? "border-red-300"
                                                        : ""
                                                }`}
                                            />
                                            {errors.title && (
                                                <p className="mt-1 text-sm text-red-600">
                                                    {errors.title}
                                                </p>
                                            )}
                                        </div>

                                        <div className="col-span-6">
                                            <label
                                                htmlFor="company_name"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Company Name
                                            </label>
                                            <input
                                                type="text"
                                                name="company_name"
                                                id="company_name"
                                                value={data.company_name}
                                                onChange={(e) =>
                                                    setData(
                                                        "company_name",
                                                        e.target.value,
                                                    )
                                                }
                                                className={`mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
                                                    errors.company_name
                                                        ? "border-red-300"
                                                        : ""
                                                }`}
                                            />
                                            {errors.company_name && (
                                                <p className="mt-1 text-sm text-red-600">
                                                    {errors.company_name}
                                                </p>
                                            )}
                                        </div>

                                        {/* Portfolio Tag */}
                                        <div className="col-span-6 sm:col-span-3">
                                            <label
                                                htmlFor="portfolio_tag_id"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Portfolio Tag
                                            </label>
                                            <select
                                                name="portfolio_tag_id"
                                                id="portfolio_tag_id"
                                                value={data.portfolio_tag_id}
                                                onChange={(e) =>
                                                    setData(
                                                        "portfolio_tag_id",
                                                        e.target.value,
                                                    )
                                                }
                                                className={`mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
                                                    errors.portfolio_tag_id
                                                        ? "border-red-300"
                                                        : ""
                                                }`}
                                            >
                                                <option value="">
                                                    Select a tag
                                                </option>
                                                {portfolioTags.map((tag) => (
                                                    <option
                                                        key={tag.id}
                                                        value={tag.id}
                                                    >
                                                        {tag.name}
                                                    </option>
                                                ))}
                                            </select>
                                            {errors.portfolio_tag_id && (
                                                <p className="mt-1 text-sm text-red-600">
                                                    {errors.portfolio_tag_id}
                                                </p>
                                            )}
                                        </div>

                                        {/* Publication Status */}
                                        <div className="col-span-6">
                                            <div className="flex items-center">
                                                <input
                                                    id="is_published"
                                                    name="is_published"
                                                    type="checkbox"
                                                    checked={data.is_published}
                                                    onChange={(e) =>
                                                        setData(
                                                            "is_published",
                                                            e.target.checked,
                                                        )
                                                    }
                                                    className={`h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ${
                                                        errors.is_published
                                                            ? "border-red-300"
                                                            : ""
                                                    }`}
                                                />
                                                <label
                                                    htmlFor="is_published"
                                                    className="ml-2 block text-sm text-gray-900"
                                                >
                                                    Published
                                                </label>
                                            </div>
                                            {errors.is_published && (
                                                <p className="mt-1 text-sm text-red-600">
                                                    {errors.is_published}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Logo Upload */}
                        <div className="bg-white px-4 py-5 shadow sm:rounded-lg sm:p-6">
                            <div className="md:grid md:grid-cols-3 md:gap-6">
                                <div className="md:col-span-1">
                                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                                        Company Logo
                                    </h3>
                                    <p className="mt-1 text-sm text-gray-500">
                                        Upload the company logo for this case
                                    </p>
                                </div>
                                <div className="mt-5 md:col-span-2 md:mt-0">
                                    <DragDropImageUpload
                                        onFileSelect={(file) => {
                                            setData("logo", file);
                                            const reader = new FileReader();
                                            reader.onload = (e) =>
                                                setLogoPreview(e.target.result);
                                            reader.readAsDataURL(file);
                                        }}
                                        preview={logoPreview}
                                        onRemove={() => {
                                            setLogoPreview(null);
                                            setData("logo", null);
                                        }}
                                        label="Upload company logo"
                                        description="PNG, JPG, GIF up to 2MB"
                                    />
                                    {errors.logo && (
                                        <p className="mt-1 text-sm text-red-600">
                                            {errors.logo}
                                        </p>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Images Upload */}
                        <div className="bg-white px-4 py-5 shadow sm:rounded-lg sm:p-6">
                            <div className="md:grid md:grid-cols-3 md:gap-6">
                                <div className="md:col-span-1">
                                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                                        Portfolio Images
                                    </h3>
                                    <p className="mt-1 text-sm text-gray-500">
                                        Upload images for this portfolio case
                                    </p>
                                </div>
                                <div className="mt-5 md:col-span-2 md:mt-0">
                                    <DragDropImageUpload
                                        onFileSelect={(files) => {
                                            const fileArray = Array.isArray(
                                                files,
                                            )
                                                ? files
                                                : [files];
                                            setData("images", [
                                                ...(data.images || []),
                                                ...fileArray,
                                            ]);

                                            // Create previews for new files
                                            fileArray.forEach((file) => {
                                                const reader = new FileReader();
                                                reader.onload = (e) => {
                                                    setImagePreviews((prev) => [
                                                        ...prev,
                                                        e.target.result,
                                                    ]);
                                                };
                                                reader.readAsDataURL(file);
                                            });
                                        }}
                                        onInsert={(files, insertIndex) => {
                                            const fileArray = Array.isArray(
                                                files,
                                            )
                                                ? files
                                                : [files];

                                            const existingImagesCount = (
                                                data.existing_images || []
                                            ).length;

                                            if (
                                                insertIndex <=
                                                existingImagesCount
                                            ) {
                                                // Insert into existing images
                                                const newExistingImages = [
                                                    ...(data.existing_images ||
                                                        []),
                                                ];
                                                // For existing images, we can't actually insert files, so add to new images
                                                // but we'll insert the previews at the correct position
                                                setData("images", [
                                                    ...(data.images || []),
                                                    ...fileArray,
                                                ]);
                                            } else {
                                                // Insert into new images
                                                const newImageIndex =
                                                    insertIndex -
                                                    existingImagesCount;
                                                const newImages = [
                                                    ...(data.images || []),
                                                ];
                                                newImages.splice(
                                                    newImageIndex,
                                                    0,
                                                    ...fileArray,
                                                );
                                                setData("images", newImages);
                                            }

                                            // Create previews and insert them at the specified position
                                            fileArray.forEach(
                                                (file, fileIndex) => {
                                                    const reader =
                                                        new FileReader();
                                                    reader.onload = (e) => {
                                                        setImagePreviews(
                                                            (prev) => {
                                                                const newPreviews =
                                                                    [...prev];
                                                                newPreviews.splice(
                                                                    insertIndex +
                                                                        fileIndex,
                                                                    0,
                                                                    e.target
                                                                        .result,
                                                                );
                                                                return newPreviews;
                                                            },
                                                        );
                                                    };
                                                    reader.readAsDataURL(file);
                                                },
                                            );
                                        }}
                                        preview={imagePreviews}
                                        onRemove={(index) => {
                                            // Handle removal of both existing and new images
                                            const existingImagesCount = (
                                                data.existing_images || []
                                            ).length;

                                            if (index < existingImagesCount) {
                                                // Remove from existing images
                                                const newExistingImages = [
                                                    ...(data.existing_images ||
                                                        []),
                                                ];
                                                newExistingImages.splice(
                                                    index,
                                                    1,
                                                );
                                                setData(
                                                    "existing_images",
                                                    newExistingImages,
                                                );
                                            } else {
                                                // Remove from new images
                                                const newImageIndex =
                                                    index - existingImagesCount;
                                                const newImages = [
                                                    ...(data.images || []),
                                                ];
                                                newImages.splice(
                                                    newImageIndex,
                                                    1,
                                                );
                                                setData("images", newImages);
                                            }

                                            // Remove from previews
                                            const newPreviews = [
                                                ...imagePreviews,
                                            ];
                                            newPreviews.splice(index, 1);
                                            setImagePreviews(newPreviews);
                                        }}
                                        onReorder={(fromIndex, toIndex) => {
                                            // Reorder previews
                                            const newPreviews = [
                                                ...imagePreviews,
                                            ];
                                            const [movedPreview] =
                                                newPreviews.splice(
                                                    fromIndex,
                                                    1,
                                                );
                                            newPreviews.splice(
                                                toIndex,
                                                0,
                                                movedPreview,
                                            );
                                            setImagePreviews(newPreviews);

                                            // Update the data arrays accordingly
                                            const existingImagesCount = (
                                                data.existing_images || []
                                            ).length;
                                            const totalImages = [
                                                ...(data.existing_images || []),
                                                ...(data.images || []),
                                            ];
                                            const [movedImage] =
                                                totalImages.splice(
                                                    fromIndex,
                                                    1,
                                                );
                                            totalImages.splice(
                                                toIndex,
                                                0,
                                                movedImage,
                                            );

                                            // Split back into existing and new
                                            const newExistingImages =
                                                totalImages.slice(
                                                    0,
                                                    existingImagesCount,
                                                );
                                            const newImages =
                                                totalImages.slice(
                                                    existingImagesCount,
                                                );

                                            setData(
                                                "existing_images",
                                                newExistingImages,
                                            );
                                            setData("images", newImages);
                                        }}
                                        multiple={true}
                                        label="Upload project images"
                                        description="PNG, JPG, GIF up to 2MB each. Drag to reorder."
                                    />
                                    {/* Display general images errors */}
                                    {errors.images && (
                                        <p className="mt-1 text-sm text-red-600">
                                            {errors.images}
                                        </p>
                                    )}
                                    {/* Display individual image errors */}
                                    {Object.keys(errors)
                                        .filter((key) =>
                                            key.startsWith("images."),
                                        )
                                        .map((key) => {
                                            const imageIndex =
                                                key.split(".")[1];
                                            return (
                                                <p
                                                    key={key}
                                                    className="mt-1 text-sm text-red-600"
                                                >
                                                    <strong>
                                                        Image{" "}
                                                        {parseInt(imageIndex) +
                                                            1}
                                                        :
                                                    </strong>{" "}
                                                    {errors[key]}
                                                </p>
                                            );
                                        })}
                                </div>
                            </div>
                        </div>

                        {/* Submit Button */}
                        <div className="flex justify-end">
                            <button
                                type="button"
                                onClick={() => router.visit("/admin/cases")}
                                className="mr-3 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                disabled={processing}
                                className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:opacity-50"
                            >
                                {processing ? "Updating..." : "Update Case"}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </>
    );
};

export default Edit;
