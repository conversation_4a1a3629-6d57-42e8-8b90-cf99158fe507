<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\Article;
use App\Models\Author;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Extract unique authors from articles writer field
        $articles = Article::whereNotNull('writer')->get();
        $uniqueAuthors = [];

        foreach ($articles as $article) {
            $writer = $article->writer;

            if (is_array($writer) && isset($writer['name'])) {
                $authorName = trim($writer['name']);
                $authorImage = isset($writer['avatar']) ? $writer['avatar'] : null;

                // Store unique authors by name (case-insensitive)
                $authorKey = strtolower($authorName);
                if (!isset($uniqueAuthors[$authorKey])) {
                    $uniqueAuthors[$authorKey] = [
                        'name' => $authorName,
                        'image' => $authorImage,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
            }
        }

        // Insert unique authors into authors table
        if (!empty($uniqueAuthors)) {
            DB::table('authors')->insert(array_values($uniqueAuthors));
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove all authors that were created from article writer data
        // This is a destructive operation, so we'll just truncate the authors table
        DB::table('authors')->truncate();
    }
};
