import { useState } from "react";
import { Head, useForm, router } from "@inertiajs/react";
import { ArrowLeftIcon } from "@phosphor-icons/react";
import DragDropImageUpload from "../../../components/DragDropImageUpload";

const Create = ({ portfolioTags = [] }) => {
    const [logoPreview, setLogoPreview] = useState(null);
    const [imagePreviews, setImagePreviews] = useState([]);

    const { data, setData, post, processing, errors } = useForm({
        title: "",
        company_name: "",
        portfolio_tag_id: "",
        logo: null,
        images: [],
        is_published: true,
    });

    const handleSubmit = (e) => {
        e.preventDefault();

        // Debug logging
        console.log("Form submission started");
        console.log("Form data:", data);
        console.log("Images count:", data.images?.length || 0);
        console.log("Logo:", data.logo ? "Present" : "Not present");

        // Validate required fields before submission
        if (!data.title.trim()) {
            console.error("Title is required");
            return;
        }
        if (!data.company_name.trim()) {
            console.error("Company name is required");
            return;
        }
        if (!data.images || data.images.length === 0) {
            console.error("At least one image is required");
            alert(
                "Please upload at least one project image before submitting.",
            );
            return;
        }

        // Check file sizes client-side
        const maxSize = 2048 * 1024; // 2MB in bytes
        const oversizedImages = [];
        data.images.forEach((image, index) => {
            if (image.size > maxSize) {
                oversizedImages.push({
                    index: index + 1,
                    name: image.name,
                    size: Math.round((image.size / 1024 / 1024) * 100) / 100, // Size in MB
                });
            }
        });

        if (oversizedImages.length > 0) {
            const errorMessage = oversizedImages
                .map((img) => `Image ${img.index} (${img.name}): ${img.size}MB`)
                .join("\n");
            alert(
                `The following images exceed the 2MB limit:\n\n${errorMessage}\n\nPlease resize or replace these images.`,
            );
            return;
        }

        console.log("Submitting to /admin/cases with forceFormData: true");
        post("/admin/cases", data, {
            forceFormData: true,
            onSuccess: (response) => {
                console.log("Success:", response);
            },
            onError: (errors) => {
                console.error("Validation errors:", errors);
                // Check for specific image size errors
                const imageErrors = Object.keys(errors).filter((key) =>
                    key.startsWith("images."),
                );
                if (imageErrors.length > 0) {
                    console.error(
                        "Individual image errors found:",
                        imageErrors.map((key) => `${key}: ${errors[key]}`),
                    );
                }
            },
            onFinish: () => {
                console.log("Request finished");
            },
        });
    };

    return (
        <>
            <Head title="Create Case" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center">
                    <button
                        onClick={() => router.visit("/admin/cases")}
                        className="mr-4 p-2 text-gray-400 hover:text-gray-600"
                    >
                        <ArrowLeftIcon className="h-5 w-5" />
                    </button>
                    <div>
                        <h1 className="text-2xl font-semibold text-gray-900">
                            Create Case
                        </h1>
                        <p className="mt-1 text-sm text-gray-600">
                            Add a new portfolio case
                        </p>
                    </div>
                </div>

                {/* Form */}
                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="bg-white px-4 py-5 shadow sm:rounded-lg sm:p-6">
                        <div className="md:grid md:grid-cols-3 md:gap-6">
                            <div className="md:col-span-1">
                                <h3 className="text-lg leading-6 font-medium text-gray-900">
                                    Case Information
                                </h3>
                                <p className="mt-1 text-sm text-gray-500">
                                    Basic information about the case
                                </p>
                            </div>
                            <div className="mt-5 md:col-span-2 md:mt-0">
                                <div className="grid grid-cols-6 gap-6">
                                    <div className="col-span-6">
                                        <label
                                            htmlFor="title"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Project Title
                                        </label>
                                        <input
                                            type="text"
                                            name="title"
                                            id="title"
                                            value={data.title}
                                            onChange={(e) =>
                                                setData("title", e.target.value)
                                            }
                                            className={`mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
                                                errors.title
                                                    ? "border-red-300"
                                                    : ""
                                            }`}
                                        />
                                        {errors.title && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors.title}
                                            </p>
                                        )}
                                    </div>

                                    <div className="col-span-6">
                                        <label
                                            htmlFor="company_name"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Company Name
                                        </label>
                                        <input
                                            type="text"
                                            name="company_name"
                                            id="company_name"
                                            value={data.company_name}
                                            onChange={(e) =>
                                                setData(
                                                    "company_name",
                                                    e.target.value,
                                                )
                                            }
                                            className={`mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
                                                errors.company_name
                                                    ? "border-red-300"
                                                    : ""
                                            }`}
                                        />
                                        {errors.company_name && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors.company_name}
                                            </p>
                                        )}
                                    </div>

                                    {/* Portfolio Tag */}
                                    <div className="col-span-6 sm:col-span-3">
                                        <label
                                            htmlFor="portfolio_tag_id"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Portfolio Tag
                                        </label>
                                        <select
                                            name="portfolio_tag_id"
                                            id="portfolio_tag_id"
                                            value={data.portfolio_tag_id}
                                            onChange={(e) =>
                                                setData(
                                                    "portfolio_tag_id",
                                                    e.target.value,
                                                )
                                            }
                                            className={`mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
                                                errors.portfolio_tag_id
                                                    ? "border-red-300"
                                                    : ""
                                            }`}
                                        >
                                            <option value="">
                                                Select a tag
                                            </option>
                                            {portfolioTags.map((tag) => (
                                                <option
                                                    key={tag.id}
                                                    value={tag.id}
                                                >
                                                    {tag.name}
                                                </option>
                                            ))}
                                        </select>
                                        {errors.portfolio_tag_id && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors.portfolio_tag_id}
                                            </p>
                                        )}
                                    </div>

                                    {/* Logo Upload */}
                                    <div className="col-span-6">
                                        <label className="block text-sm font-medium text-gray-700">
                                            Company Logo
                                        </label>
                                        <div className="mt-1">
                                            <DragDropImageUpload
                                                onFileSelect={(file) => {
                                                    setData("logo", file);
                                                    const reader =
                                                        new FileReader();
                                                    reader.onload = (e) =>
                                                        setLogoPreview(
                                                            e.target.result,
                                                        );
                                                    reader.readAsDataURL(file);
                                                }}
                                                preview={logoPreview}
                                                onRemove={() => {
                                                    setLogoPreview(null);
                                                    setData("logo", null);
                                                }}
                                                label="Upload company logo"
                                                description="PNG, JPG, GIF up to 2MB"
                                            />
                                        </div>
                                        {errors.logo && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors.logo}
                                            </p>
                                        )}
                                    </div>

                                    {/* Images Upload */}
                                    <div className="col-span-6">
                                        <label className="block text-sm font-medium text-gray-700">
                                            Project Images
                                        </label>
                                        <div className="mt-1">
                                            <DragDropImageUpload
                                                onFileSelect={(files) => {
                                                    const fileArray =
                                                        Array.isArray(files)
                                                            ? files
                                                            : [files];
                                                    setData("images", [
                                                        ...(data.images || []),
                                                        ...fileArray,
                                                    ]);

                                                    // Create previews
                                                    fileArray.forEach(
                                                        (file) => {
                                                            const reader =
                                                                new FileReader();
                                                            reader.onload = (
                                                                e,
                                                            ) => {
                                                                setImagePreviews(
                                                                    (prev) => [
                                                                        ...prev,
                                                                        e.target
                                                                            .result,
                                                                    ],
                                                                );
                                                            };
                                                            reader.readAsDataURL(
                                                                file,
                                                            );
                                                        },
                                                    );
                                                }}
                                                onInsert={(
                                                    files,
                                                    insertIndex,
                                                ) => {
                                                    const fileArray =
                                                        Array.isArray(files)
                                                            ? files
                                                            : [files];

                                                    // Insert files at the specified position
                                                    const newImages = [
                                                        ...(data.images || []),
                                                    ];
                                                    newImages.splice(
                                                        insertIndex,
                                                        0,
                                                        ...fileArray,
                                                    );
                                                    setData(
                                                        "images",
                                                        newImages,
                                                    );

                                                    // Create previews and insert them at the same position
                                                    fileArray.forEach(
                                                        (file, fileIndex) => {
                                                            const reader =
                                                                new FileReader();
                                                            reader.onload = (
                                                                e,
                                                            ) => {
                                                                setImagePreviews(
                                                                    (prev) => {
                                                                        const newPreviews =
                                                                            [
                                                                                ...prev,
                                                                            ];
                                                                        newPreviews.splice(
                                                                            insertIndex +
                                                                                fileIndex,
                                                                            0,
                                                                            e
                                                                                .target
                                                                                .result,
                                                                        );
                                                                        return newPreviews;
                                                                    },
                                                                );
                                                            };
                                                            reader.readAsDataURL(
                                                                file,
                                                            );
                                                        },
                                                    );
                                                }}
                                                preview={imagePreviews}
                                                onRemove={(index) => {
                                                    const newImages = [
                                                        ...(data.images || []),
                                                    ];
                                                    const newPreviews = [
                                                        ...imagePreviews,
                                                    ];
                                                    newImages.splice(index, 1);
                                                    newPreviews.splice(
                                                        index,
                                                        1,
                                                    );
                                                    setData(
                                                        "images",
                                                        newImages,
                                                    );
                                                    setImagePreviews(
                                                        newPreviews,
                                                    );
                                                }}
                                                onReorder={(
                                                    fromIndex,
                                                    toIndex,
                                                ) => {
                                                    const newImages = [
                                                        ...(data.images || []),
                                                    ];
                                                    const newPreviews = [
                                                        ...imagePreviews,
                                                    ];

                                                    // Reorder both arrays
                                                    const [movedImage] =
                                                        newImages.splice(
                                                            fromIndex,
                                                            1,
                                                        );
                                                    const [movedPreview] =
                                                        newPreviews.splice(
                                                            fromIndex,
                                                            1,
                                                        );
                                                    newImages.splice(
                                                        toIndex,
                                                        0,
                                                        movedImage,
                                                    );
                                                    newPreviews.splice(
                                                        toIndex,
                                                        0,
                                                        movedPreview,
                                                    );

                                                    setData(
                                                        "images",
                                                        newImages,
                                                    );
                                                    setImagePreviews(
                                                        newPreviews,
                                                    );
                                                }}
                                                multiple={true}
                                                label="Upload project images"
                                                description="PNG, JPG, GIF up to 2MB each. Drag to reorder."
                                            />
                                        </div>
                                        {/* Display general images errors */}
                                        {errors.images && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors.images}
                                            </p>
                                        )}
                                        {/* Display individual image errors */}
                                        {Object.keys(errors)
                                            .filter((key) =>
                                                key.startsWith("images."),
                                            )
                                            .map((key) => {
                                                const imageIndex =
                                                    key.split(".")[1];
                                                return (
                                                    <p
                                                        key={key}
                                                        className="mt-1 text-sm text-red-600"
                                                    >
                                                        <strong>
                                                            Image{" "}
                                                            {parseInt(
                                                                imageIndex,
                                                            ) + 1}
                                                            :
                                                        </strong>{" "}
                                                        {errors[key]}
                                                    </p>
                                                );
                                            })}
                                    </div>

                                    {/* Published Status */}
                                    <div className="col-span-6">
                                        <div className="flex items-center">
                                            <input
                                                id="is_published"
                                                name="is_published"
                                                type="checkbox"
                                                checked={data.is_published}
                                                onChange={(e) =>
                                                    setData(
                                                        "is_published",
                                                        e.target.checked,
                                                    )
                                                }
                                                className={`h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ${
                                                    errors.is_published
                                                        ? "border-red-300"
                                                        : ""
                                                }`}
                                            />
                                            <label
                                                htmlFor="is_published"
                                                className="ml-2 block text-sm text-gray-900"
                                            >
                                                Published
                                            </label>
                                        </div>
                                        {errors.is_published && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors.is_published}
                                            </p>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Submit Button */}
                    <div className="flex justify-end">
                        <button
                            type="button"
                            onClick={() => router.visit("/admin/cases")}
                            className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled={processing}
                            className="ml-3 inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:opacity-50"
                        >
                            {processing ? "Creating..." : "Create Case"}
                        </button>
                    </div>
                </form>
            </div>
        </>
    );
};

export default Create;
