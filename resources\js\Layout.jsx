import DesktopHeader from "./components/DesktopHeader";
import MobileHeader from "./components/MobileHeader";
import Footer from "./components/Footer";
import SkipLink from "./components/SkipLink";
import { usePage } from "@inertiajs/react";
import { useEffect } from "react";
import { navLinks, getActiveNavItem } from "./constants/navigation";
import { Toaster } from "react-hot-toast";

const Layout = ({ children }) => {
    const page = usePage();
    const { url, currentRouteName } = page.props;

    // Use route-based active detection instead of URL matching
    const activeLink = getActiveNavItem(currentRouteName);

    const isHome = currentRouteName === "home";

    useEffect(() => {
        window.scrollTo({
            top: 0,
            left: 0,
            behavior: "smooth",
        });
    }, [url]);

    return (
        <div
            className={`relative ${isHome ? "bg-[rgb(245,246,249)]" : "bg-white"}`}
        >
            <SkipLink />
            <DesktopHeader navLinks={navLinks} activeLink={activeLink} />
            <MobileHeader navLinks={navLinks} activeLink={activeLink} />
            <main id="main-content" role="main">
                {children}
            </main>
            <Footer />
            <Toaster
                position="top-center"
                toastOptions={{
                    duration: 4000,
                    style: {
                        background: "#ffffff",
                        color: "#1f2937",
                        border: "1px solid #e5e7eb",
                        borderRadius: "12px",
                        boxShadow:
                            "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
                        padding: "16px",
                        fontSize: "14px",
                        fontWeight: "500",
                    },
                    success: {
                        iconTheme: {
                            primary: "#56d08a",
                            secondary: "#ffffff",
                        },
                    },
                }}
            />
        </div>
    );
};

export default Layout;
